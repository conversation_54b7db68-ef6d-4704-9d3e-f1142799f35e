{"version": 3, "names": ["makeMutable", "makeViewDescriptorsSet", "shareableViewDescriptors", "data", "add", "item", "modify", "descriptors", "index", "findIndex", "descriptor", "tag", "push", "remove", "viewTag", "splice"], "sourceRoot": "../../src", "sources": ["ViewDescriptorsSet.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,WAAW,QAAQ,WAAQ;AASpC,OAAO,SAASC,sBAAsBA,CAAA,EAAuB;EAC3D,MAAMC,wBAAwB,GAAGF,WAAW,CAAe,EAAE,CAAC;EAC9D,MAAMG,IAAwB,GAAG;IAC/BD,wBAAwB;IACxBE,GAAG,EAAGC,IAAgB,IAAK;MACzBH,wBAAwB,CAACI,MAAM,CAAEC,WAAW,IAAK;QAC/C,SAAS;;QACT,MAAMC,KAAK,GAAGD,WAAW,CAACE,SAAS,CAChCC,UAAU,IAAKA,UAAU,CAACC,GAAG,KAAKN,IAAI,CAACM,GAC1C,CAAC;QACD,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBD,WAAW,CAACC,KAAK,CAAC,GAAGH,IAAI;QAC3B,CAAC,MAAM;UACLE,WAAW,CAACK,IAAI,CAACP,IAAI,CAAC;QACxB;QACA,OAAOE,WAAW;MACpB,CAAC,EAAE,KAAK,CAAC;IACX,CAAC;IAEDM,MAAM,EAAGC,OAAe,IAAK;MAC3BZ,wBAAwB,CAACI,MAAM,CAAEC,WAAW,IAAK;QAC/C,SAAS;;QACT,MAAMC,KAAK,GAAGD,WAAW,CAACE,SAAS,CAChCC,UAAU,IAAKA,UAAU,CAACC,GAAG,KAAKG,OACrC,CAAC;QACD,IAAIN,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBD,WAAW,CAACQ,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;QAC9B;QACA,OAAOD,WAAW;MACpB,CAAC,EAAE,KAAK,CAAC;IACX;EACF,CAAC;EACD,OAAOJ,IAAI;AACb", "ignoreList": []}