{"version": 3, "names": ["logger", "isChromeDebugger", "isJest", "shouldBeUseWeb", "setGestureState", "setGestureStateNative", "handlerTag", "newState", "_WORKLET", "warn", "global", "_setGestureState", "setGestureStateJest", "setGestureStateChromeDebugger", "setGestureStateDefault"], "sourceRoot": "../../../src", "sources": ["platformFunctions/setGestureState.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,oBAAW;AAClC,SAASC,gBAAgB,EAAEC,MAAM,EAAEC,cAAc,QAAQ,uBAAoB;AAI7E,OAAO,IAAIC,eAAgC;AAE3C,SAASC,qBAAqBA,CAACC,UAAkB,EAAEC,QAAgB,EAAE;EACnE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbR,MAAM,CAACS,IAAI,CAAC,0DAA0D,CAAC;IACvE;EACF;EACAC,MAAM,CAACC,gBAAgB,CAACL,UAAU,EAAEC,QAAQ,CAAC;AAC/C;AAEA,SAASK,mBAAmBA,CAAA,EAAG;EAC7BZ,MAAM,CAACS,IAAI,CAAC,6CAA6C,CAAC;AAC5D;AAEA,SAASI,6BAA6BA,CAAA,EAAG;EACvCb,MAAM,CAACS,IAAI,CAAC,wDAAwD,CAAC;AACvE;AAEA,SAASK,sBAAsBA,CAAA,EAAG;EAChCd,MAAM,CAACS,IAAI,CAAC,2DAA2D,CAAC;AAC1E;AAEA,IAAI,CAACN,cAAc,CAAC,CAAC,EAAE;EACrBC,eAAe,GAAGC,qBAAqB;AACzC,CAAC,MAAM,IAAIH,MAAM,CAAC,CAAC,EAAE;EACnBE,eAAe,GAAGQ,mBAAmB;AACvC,CAAC,MAAM,IAAIX,gBAAgB,CAAC,CAAC,EAAE;EAC7BG,eAAe,GAAGS,6BAA6B;AACjD,CAAC,MAAM;EACLT,eAAe,GAAGU,sBAAsB;AAC1C", "ignoreList": []}