{"version": 3, "names": ["shouldBeUseWeb", "useAnimatedStyle", "useAnimatedPropsJS", "updater", "deps", "adapters", "useAnimatedPropsNative", "useAnimatedProps"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedProps.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,uBAAoB;AAEnD,SAASC,gBAAgB,QAAQ,uBAAoB;;AAErD;;AAYA,SAASC,kBAAkBA,CACzBC,OAAoB,EACpBC,IAA4B,EAC5BC,QAGQ,EACR;EACA,OAAQJ,gBAAgB,CACtBE,OAAO,EACPC,IAAI,EACJC,QAAQ,EACR,IACF,CAAC;AACH;AAEA,MAAMC,sBAAsB,GAAGL,gBAAgB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,gBAAkC,GAAGP,cAAc,CAAC,CAAC,GAC7DE,kBAAkB,GACnBI,sBAAsB", "ignoreList": []}