{"version": 3, "names": ["ReanimatedError", "WorkletsTurboModule", "getValueUnpackerCode", "createNativeWorkletsModule", "NativeWorklets", "workletsModuleProxy", "constructor", "global", "__workletsModuleProxy", "undefined", "valueUnpackerCode", "installTurboModule", "makeShareableClone", "value", "shouldPersistRemote", "nativeStateSource"], "sourceRoot": "../../../../src", "sources": ["worklets/WorkletsModule/NativeWorklets.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,iBAAc;AAC9C,SAASC,mBAAmB,QAAQ,sBAAa;AACjD,SAASC,oBAAoB,QAAQ,qBAAkB;AAGvD,OAAO,SAASC,0BAA0BA,CAAA,EAAoB;EAC5D,OAAO,IAAIC,cAAc,CAAC,CAAC;AAC7B;AAEA,MAAMA,cAAc,CAAC;EACnB,CAACC,mBAAmB;EAEpBC,WAAWA,CAAA,EAAG;IACZ,IAAIC,MAAM,CAACC,qBAAqB,KAAKC,SAAS,EAAE;MAC9C,MAAMC,iBAAiB,GAAGR,oBAAoB,CAAC,CAAC;MAChDD,mBAAmB,EAAEU,kBAAkB,CAACD,iBAAiB,CAAC;IAC5D;IACA,IAAIH,MAAM,CAACC,qBAAqB,KAAKC,SAAS,EAAE;MAC9C,MAAM,IAAIT,eAAe,CACvB;AACR,6JACM,CAAC;IACH;IACA,IAAI,CAAC,CAACK,mBAAmB,GAAGE,MAAM,CAACC,qBAAqB;EAC1D;EAEAI,kBAAkBA,CAChBC,KAAQ,EACRC,mBAA4B,EAC5BC,iBAA0B,EAC1B;IACA,OAAO,IAAI,CAAC,CAACV,mBAAmB,CAACO,kBAAkB,CACjDC,KAAK,EACLC,mBAAmB,EACnBC,iBACF,CAAC;EACH;AACF", "ignoreList": []}