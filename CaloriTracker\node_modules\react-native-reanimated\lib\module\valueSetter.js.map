{"version": 3, "names": ["valueSetter", "mutable", "value", "forceUpdate", "previousAnimation", "_animation", "cancelled", "onFrame", "undefined", "animation", "_value", "current", "isHigherOrder", "callback", "initializeAnimation", "timestamp", "onStart", "currentTimestamp", "global", "__frameTimestamp", "_getAnimationTimestamp", "step", "newTimestamp", "finished", "requestAnimationFrame"], "sourceRoot": "../../src", "sources": ["valueSetter.ts"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,WAAWA,CACzBC,OAAuB,EACvBC,KAAY,EACZC,WAAW,GAAG,KAAK,EACb;EACN,SAAS;;EACT,MAAMC,iBAAiB,GAAGH,OAAO,CAACI,UAAU;EAC5C,IAAID,iBAAiB,EAAE;IACrBA,iBAAiB,CAACE,SAAS,GAAG,IAAI;IAClCL,OAAO,CAACI,UAAU,GAAG,IAAI;EAC3B;EACA,IACE,OAAOH,KAAK,KAAK,UAAU,IAC1BA,KAAK,KAAK,IAAI,IACb,OAAOA,KAAK,KAAK,QAAQ;EACzB;EACCA,KAAK,CAAgCK,OAAO,KAAKC,SAAU,EAC9D;IACA,MAAMC,SAAiC,GACrC,OAAOP,KAAK,KAAK,UAAU;IACvB;IACCA,KAAK,CAAkC,CAAC;IACzC;IACCA,KAA2C;IAClD;IACA;IACA;IACA;IACA,IACED,OAAO,CAACS,MAAM,KAAKD,SAAS,CAACE,OAAO,IACpC,CAACF,SAAS,CAACG,aAAa,IACxB,CAACT,WAAW,EACZ;MACAM,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAI,CAAC;MAC9C;IACF;IACA;IACA,MAAMC,mBAAmB,GAAIC,SAAiB,IAAK;MACjDN,SAAS,CAACO,OAAO,CAACP,SAAS,EAAER,OAAO,CAACC,KAAK,EAAEa,SAAS,EAAEX,iBAAiB,CAAC;IAC3E,CAAC;IACD,MAAMa,gBAAgB,GACpBC,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC;IAC5DN,mBAAmB,CAACG,gBAAgB,CAAC;IAErC,MAAMI,IAAI,GAAIC,YAAoB,IAAK;MACrC;MACA;MACA;;MAEA,MAAMP,SAAS,GACbO,YAAY,IAAIb,SAAS,CAACM,SAAS,IAAI,CAAC,CAAC,GACrCN,SAAS,CAACM,SAAS,GACnBO,YAAY;MAElB,IAAIb,SAAS,CAACH,SAAS,EAAE;QACvBG,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC;QAC9D;MACF;MACA,MAAMU,QAAQ,GAAGd,SAAS,CAACF,OAAO,CAACE,SAAS,EAAEM,SAAS,CAAC;MACxDN,SAAS,CAACc,QAAQ,GAAG,IAAI;MACzBd,SAAS,CAACM,SAAS,GAAGA,SAAS;MAC/B;MACA;MACA;MACAd,OAAO,CAACS,MAAM,GAAGD,SAAS,CAACE,OAAQ;MACnC,IAAIY,QAAQ,EAAE;QACZd,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;MAC/D,CAAC,MAAM;QACLW,qBAAqB,CAACH,IAAI,CAAC;MAC7B;IACF,CAAC;IAEDpB,OAAO,CAACI,UAAU,GAAGI,SAAS;IAE9BY,IAAI,CAACJ,gBAAgB,CAAC;EACxB,CAAC,MAAM;IACL;IACA;IACA,IAAIhB,OAAO,CAACS,MAAM,KAAKR,KAAK,IAAI,CAACC,WAAW,EAAE;MAC5C;IACF;IACAF,OAAO,CAACS,MAAM,GAAGR,KAAK;EACxB;AACF", "ignoreList": []}