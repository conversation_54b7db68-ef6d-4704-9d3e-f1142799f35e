{"version": 3, "names": ["useCallback", "useEffect", "useRef", "isWeb", "useEvent", "useSharedValue", "IS_WEB", "useScrollViewOffset", "useScrollViewOffsetWeb", "useScrollViewOffsetNative", "animatedRef", "providedOffset", "internalOffset", "offset", "current", "<PERSON><PERSON><PERSON><PERSON>", "element", "getWebScrollableElement", "value", "scrollLeft", "scrollTop", "addEventListener", "removeEventListener", "event", "contentOffset", "x", "y", "scrollNativeEventNames", "elementTag", "getTag", "workletEventHandler", "registerForEvents", "unregisterFromEvents", "scrollComponent", "getScrollableNode"], "sourceRoot": "../../../src", "sources": ["hook/useScrollViewOffset.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAItD,SAASC,KAAK,QAAQ,uBAAoB;AAO1C,SAASC,QAAQ,QAAQ,eAAY;AACrC,SAASC,cAAc,QAAQ,qBAAkB;AAEjD,MAAMC,MAAM,GAAGH,KAAK,CAAC,CAAC;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,mBAAmB,GAAGD,MAAM,GACrCE,sBAAsB,GACtBC,yBAAyB;AAE7B,SAASD,sBAAsBA,CAC7BE,WAAmD,EACnDC,cAAoC,EACf;EACrB,MAAMC,cAAc,GAAGP,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMQ,MAAM,GAAGX,MAAM,CAACS,cAAc,IAAIC,cAAc,CAAC,CAACE,OAAO;EAE/D,MAAMC,YAAY,GAAGf,WAAW,CAAC,MAAM;IACrC,SAAS;;IACT,IAAIU,WAAW,EAAE;MACf,MAAMM,OAAO,GAAGC,uBAAuB,CAACP,WAAW,CAACI,OAAO,CAAC;MAC5D;MACAD,MAAM,CAACK,KAAK,GACVF,OAAO,CAACG,UAAU,KAAK,CAAC,GAAGH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACG,UAAU;IACrE;IACA;EACF,CAAC,EAAE,CAACT,WAAW,EAAEA,WAAW,EAAEI,OAAO,CAAC,CAAC;EAEvCb,SAAS,CAAC,MAAM;IACd,MAAMe,OAAO,GAAGN,WAAW,EAAEI,OAAO,GAChCG,uBAAuB,CAACP,WAAW,CAACI,OAAO,CAAC,GAC5C,IAAI;IAER,IAAIE,OAAO,EAAE;MACXA,OAAO,CAACK,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAClD;IACA,OAAO,MAAM;MACX,IAAIC,OAAO,EAAE;QACXA,OAAO,CAACM,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MACrD;IACF,CAAC;IACD;IACA;IACA;IACA;EACF,CAAC,EAAE,CAACL,WAAW,EAAEA,WAAW,EAAEI,OAAO,EAAEC,YAAY,CAAC,CAAC;EAErD,OAAOF,MAAM;AACf;AAEA,SAASJ,yBAAyBA,CAChCC,WAAmD,EACnDC,cAAoC,EACf;EACrB,MAAMC,cAAc,GAAGP,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMQ,MAAM,GAAGX,MAAM,CAACS,cAAc,IAAIC,cAAc,CAAC,CAACE,OAAO;EAE/D,MAAMC,YAAY,GAAGX,QAAQ,CAC1BmB,KAA4B,IAAK;IAChC,SAAS;;IACTV,MAAM,CAACK,KAAK,GACVK,KAAK,CAACC,aAAa,CAACC,CAAC,KAAK,CAAC,GACvBF,KAAK,CAACC,aAAa,CAACE,CAAC,GACrBH,KAAK,CAACC,aAAa,CAACC,CAAC;EAC7B,CAAC,EACDE;EACA;EACA;EACF,CAA2D;EAE3D1B,SAAS,CAAC,MAAM;IACd,MAAM2B,UAAU,GAAGlB,WAAW,EAAEmB,MAAM,CAAC,CAAC,IAAI,IAAI;IAEhD,IAAID,UAAU,EAAE;MACdb,YAAY,CAACe,mBAAmB,CAACC,iBAAiB,CAACH,UAAU,CAAC;IAChE;IACA,OAAO,MAAM;MACX,IAAIA,UAAU,EAAE;QACdb,YAAY,CAACe,mBAAmB,CAACE,oBAAoB,CAACJ,UAAU,CAAC;MACnE;IACF,CAAC;IACD;IACA;IACA;IACA;EACF,CAAC,EAAE,CAAClB,WAAW,EAAEA,WAAW,EAAEI,OAAO,EAAEC,YAAY,CAAC,CAAC;EAErD,OAAOF,MAAM;AACf;AAEA,SAASI,uBAAuBA,CAC9BgB,eAA0C,EAC7B;EACb,OACGA,eAAe,EAAEC,iBAAiB,CAAC,CAAC,IACrCD,eAAe;AAEnB;AAEA,MAAMN,sBAAsB,GAAG,CAC7B,UAAU,EACV,mBAAmB,EACnB,iBAAiB,EACjB,uBAAuB,EACvB,qBAAqB,CACtB", "ignoreList": []}