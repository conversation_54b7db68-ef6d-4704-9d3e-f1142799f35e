import React, { useEffect } from 'react';
import { Pressable, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import LinearGradient from 'react-native-linear-gradient';
import { theme } from '../../theme/theme';

/**
 * Modern Floating Action Button with Animations
 */
const FloatingActionButton = ({
  onPress,
  children,
  icon,
  style,
  size = 56,
  colors = [theme.colors.primary, theme.colors.primaryContainer],
  disabled = false,
  position = 'bottomRight',
  offset = theme.spacing.lg,
  elevation = 'xl',
  animationType = 'scale',
  ...props
}) => {
  const scale = useSharedValue(0);
  const rotation = useSharedValue(0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    // Entry animation
    scale.value = withSpring(1, theme.animations.spring.bouncy);
    opacity.value = withTiming(1, { duration: theme.animations.duration.medium });
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const scaleValue = interpolate(
      scale.value,
      [0, 1],
      [0, 1],
      Extrapolate.CLAMP
    );

    return {
      transform: [
        { scale: scaleValue },
        { rotate: `${rotation.value}deg` },
      ],
      opacity: opacity.value,
    };
  });

  const handlePressIn = () => {
    if (disabled) return;
    
    switch (animationType) {
      case 'scale':
        scale.value = withSpring(0.9, theme.animations.spring.snappy);
        break;
      case 'rotate':
        rotation.value = withSpring(15, theme.animations.spring.gentle);
        break;
    }
  };

  const handlePressOut = () => {
    if (disabled) return;
    
    switch (animationType) {
      case 'scale':
        scale.value = withSpring(1, theme.animations.spring.bouncy);
        break;
      case 'rotate':
        rotation.value = withSpring(0, theme.animations.spring.gentle);
        break;
    }
  };

  const getPositionStyle = () => {
    const baseStyle = {
      position: 'absolute',
    };

    switch (position) {
      case 'bottomRight':
        return { ...baseStyle, bottom: offset, right: offset };
      case 'bottomLeft':
        return { ...baseStyle, bottom: offset, left: offset };
      case 'topRight':
        return { ...baseStyle, top: offset, right: offset };
      case 'topLeft':
        return { ...baseStyle, top: offset, left: offset };
      case 'center':
        return { ...baseStyle, alignSelf: 'center' };
      default:
        return { ...baseStyle, bottom: offset, right: offset };
    }
  };

  const fabStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
    ...theme.shadows[elevation],
  };

  return (
    <Animated.View
      style={[
        styles.container,
        fabStyle,
        getPositionStyle(),
        animatedStyle,
        style,
        disabled && styles.disabled,
      ]}
    >
      <Pressable
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        style={styles.pressable}
        {...props}
      >
        <LinearGradient
          colors={colors}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {children || icon}
        </LinearGradient>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  pressable: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
});

export default FloatingActionButton;
