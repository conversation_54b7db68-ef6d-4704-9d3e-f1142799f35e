{"version": 3, "names": ["ComplexAnimationBuilder", "RotateInDownLeft", "presetName", "createInstance", "build", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "values", "animations", "opacity", "transform", "rotate", "translateX", "translateY", "targetWidth", "targetHeight", "RotateInDownRight", "RotateInUpLeft", "RotateInUpRight", "RotateOutDownLeft", "currentWidth", "currentHeight", "RotateOutDownRight", "RotateOutUpLeft", "RotateOutUpRight"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultAnimations/Rotate.ts"], "mappings": "AAAA,YAAY;;AASZ,SAASA,uBAAuB,QAAQ,8BAAqB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,SACnBD,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,kBAAkB;EAEtC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIF,gBAAgB,CAAC,CAAC;EAC/B;EAEAG,KAAK,GAAGA,CAAA,KAAsD;IAC5D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDU,SAAS,EAAE,CACT;YAAEC,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC3D;YAAEY,UAAU,EAAEf,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC1D;YAAEa,UAAU,EAAEhB,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC;QAE9D,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CACT;YAAEC,MAAM,EAAE;UAAS,CAAC,EACpB;YAAEC,UAAU,EAAEL,MAAM,CAACO,WAAW,GAAG,CAAC,GAAGP,MAAM,CAACQ,YAAY,GAAG;UAAE,CAAC,EAChE;YAAEF,UAAU,EAAE,EAAEN,MAAM,CAACO,WAAW,GAAG,CAAC,GAAGP,MAAM,CAACQ,YAAY,GAAG,CAAC;UAAE,CAAC,CACpE;UACD,GAAGT;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMY,iBAAiB,SACpBxB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,mBAAmB;EAEvC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIqB,iBAAiB,CAAC,CAAC;EAChC;EAEApB,KAAK,GAAGA,CAAA,KAAsD;IAC5D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDU,SAAS,EAAE,CACT;YAAEC,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC3D;YAAEY,UAAU,EAAEf,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC1D;YAAEa,UAAU,EAAEhB,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC;QAE9D,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CACT;YAAEC,MAAM,EAAE;UAAQ,CAAC,EACnB;YAAEC,UAAU,EAAE,EAAEL,MAAM,CAACO,WAAW,GAAG,CAAC,GAAGP,MAAM,CAACQ,YAAY,GAAG,CAAC;UAAE,CAAC,EACnE;YAAEF,UAAU,EAAE,EAAEN,MAAM,CAACO,WAAW,GAAG,CAAC,GAAGP,MAAM,CAACQ,YAAY,GAAG,CAAC;UAAE,CAAC,CACpE;UACD,GAAGT;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMa,cAAc,SACjBzB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,gBAAgB;EAEpC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIsB,cAAc,CAAC,CAAC;EAC7B;EAEArB,KAAK,GAAGA,CAAA,KAAsD;IAC5D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDU,SAAS,EAAE,CACT;YAAEC,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC3D;YAAEY,UAAU,EAAEf,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC1D;YAAEa,UAAU,EAAEhB,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC;QAE9D,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CACT;YAAEC,MAAM,EAAE;UAAQ,CAAC,EACnB;YAAEC,UAAU,EAAEL,MAAM,CAACO,WAAW,GAAG,CAAC,GAAGP,MAAM,CAACQ,YAAY,GAAG;UAAE,CAAC,EAChE;YAAEF,UAAU,EAAEN,MAAM,CAACO,WAAW,GAAG,CAAC,GAAGP,MAAM,CAACQ,YAAY,GAAG;UAAE,CAAC,CACjE;UACD,GAAGT;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,eAAe,SAClB1B,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,iBAAiB;EAErC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIuB,eAAe,CAAC,CAAC;EAC9B;EAEAtB,KAAK,GAAGA,CAAA,KAAsD;IAC5D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDU,SAAS,EAAE,CACT;YAAEC,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC3D;YAAEY,UAAU,EAAEf,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC1D;YAAEa,UAAU,EAAEhB,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC;QAE9D,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CACT;YAAEC,MAAM,EAAE;UAAS,CAAC,EACpB;YAAEC,UAAU,EAAE,EAAEL,MAAM,CAACO,WAAW,GAAG,CAAC,GAAGP,MAAM,CAACQ,YAAY,GAAG,CAAC;UAAE,CAAC,EACnE;YAAEF,UAAU,EAAEN,MAAM,CAACO,WAAW,GAAG,CAAC,GAAGP,MAAM,CAACQ,YAAY,GAAG;UAAE,CAAC,CACjE;UACD,GAAGT;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMe,iBAAiB,SACpB3B,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,mBAAmB;EAEvC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIwB,iBAAiB,CAAC,CAAC;EAChC;EAEAvB,KAAK,GAAGA,CAAA,KAAqD;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDU,SAAS,EAAE,CACT;YAAEC,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,OAAO,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC5D;YACEY,UAAU,EAAEf,aAAa,CACvBK,KAAK,EACLH,SAAS,CACPQ,MAAM,CAACa,YAAY,GAAG,CAAC,GAAGb,MAAM,CAACc,aAAa,GAAG,CAAC,EAClDrB,MACF,CACF;UACF,CAAC,EACD;YACEa,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CACPQ,MAAM,CAACa,YAAY,GAAG,CAAC,GAAGb,MAAM,CAACc,aAAa,GAAG,CAAC,EAClDrB,MACF,CACF;UACF,CAAC;QAEL,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAC,CAAC;UACrE,GAAGP;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkB,kBAAkB,SACrB9B,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,oBAAoB;EAExC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI2B,kBAAkB,CAAC,CAAC;EACjC;EAEA1B,KAAK,GAAGA,CAAA,KAAqD;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDU,SAAS,EAAE,CACT;YAAEC,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC7D;YACEY,UAAU,EAAEf,aAAa,CACvBK,KAAK,EACLH,SAAS,CACP,EAAEQ,MAAM,CAACa,YAAY,GAAG,CAAC,GAAGb,MAAM,CAACc,aAAa,GAAG,CAAC,CAAC,EACrDrB,MACF,CACF;UACF,CAAC,EACD;YACEa,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CACPQ,MAAM,CAACa,YAAY,GAAG,CAAC,GAAGb,MAAM,CAACc,aAAa,GAAG,CAAC,EAClDrB,MACF,CACF;UACF,CAAC;QAEL,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAC,CAAC;UACrE,GAAGP;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,eAAe,SAClB/B,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,iBAAiB;EAErC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI4B,eAAe,CAAC,CAAC;EAC9B;EAEA3B,KAAK,GAAGA,CAAA,KAAqD;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDU,SAAS,EAAE,CACT;YAAEC,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC7D;YACEY,UAAU,EAAEf,aAAa,CACvBK,KAAK,EACLH,SAAS,CACPQ,MAAM,CAACa,YAAY,GAAG,CAAC,GAAGb,MAAM,CAACc,aAAa,GAAG,CAAC,EAClDrB,MACF,CACF;UACF,CAAC,EACD;YACEa,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CACP,EAAEQ,MAAM,CAACa,YAAY,GAAG,CAAC,GAAGb,MAAM,CAACc,aAAa,GAAG,CAAC,CAAC,EACrDrB,MACF,CACF;UACF,CAAC;QAEL,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAC,CAAC;UACrE,GAAGP;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoB,gBAAgB,SACnBhC,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,kBAAkB;EAEtC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI6B,gBAAgB,CAAC,CAAC;EAC/B;EAEA5B,KAAK,GAAGA,CAAA,KAAqD;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;UACnDU,SAAS,EAAE,CACT;YAAEC,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,OAAO,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC5D;YACEY,UAAU,EAAEf,aAAa,CACvBK,KAAK,EACLH,SAAS,CACP,EAAEQ,MAAM,CAACa,YAAY,GAAG,CAAC,GAAGb,MAAM,CAACc,aAAa,GAAG,CAAC,CAAC,EACrDrB,MACF,CACF;UACF,CAAC,EACD;YACEa,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CACP,EAAEQ,MAAM,CAACa,YAAY,GAAG,CAAC,GAAGb,MAAM,CAACc,aAAa,GAAG,CAAC,CAAC,EACrDrB,MACF,CACF;UACF,CAAC;QAEL,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAC,CAAC;UACrE,GAAGP;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH", "ignoreList": []}