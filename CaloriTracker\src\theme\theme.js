import { COLORS } from '../config/config';
import { Appearance } from 'react-native';

/**
 * Modern App Theme Configuration with Dark Mode Support
 */

// Get system theme preference
const getSystemTheme = () => {
  return Appearance.getColorScheme() || 'light';
};

// Create theme based on mode
const createTheme = (mode = 'light') => {
  const colors = COLORS[mode];

  return {
    mode,
    colors: {
      // Material Design 3 colors
      primary: colors.primary,
      primaryContainer: colors.primaryContainer,
      onPrimary: colors.onPrimary,
      onPrimaryContainer: colors.onPrimaryContainer,

      secondary: colors.secondary,
      secondaryContainer: colors.secondaryContainer,
      onSecondary: colors.onSecondary,
      onSecondaryContainer: colors.onSecondaryContainer,

      tertiary: colors.tertiary,
      tertiaryContainer: colors.tertiaryContainer,
      onTertiary: colors.onTertiary,
      onTertiaryContainer: colors.onTertiaryContainer,

      background: colors.background,
      onBackground: colors.onBackground,
      surface: colors.surface,
      onSurface: colors.onSurface,
      surfaceVariant: colors.surfaceVariant,
      onSurfaceVariant: colors.onSurfaceVariant,

      outline: colors.outline,
      outlineVariant: colors.outlineVariant,
      shadow: colors.shadow,
      scrim: colors.scrim,

      error: colors.error,
      errorContainer: colors.errorContainer,
      onError: colors.onError,
      onErrorContainer: colors.onErrorContainer,

      // Nutrition colors with gradients
      calories: colors.calories,
      protein: colors.protein,
      carbs: colors.carbs,
      fat: colors.fat,

      // Status colors
      success: colors.success,
      successContainer: colors.successContainer,
      warning: colors.warning,
      warningContainer: colors.warningContainer,
      info: colors.info,
      infoContainer: colors.infoContainer,

      // Additional utility colors
      transparent: 'transparent',
      overlay: mode === 'light' ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255, 255, 255, 0.1)',
      lightOverlay: mode === 'light' ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255, 255, 255, 0.05)',
      cardOverlay: mode === 'light' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(28, 27, 31, 0.9)',

      // Glassmorphism colors
      glass: mode === 'light' ? 'rgba(255, 255, 255, 0.25)' : 'rgba(255, 255, 255, 0.1)',
      glassBorder: mode === 'light' ? 'rgba(255, 255, 255, 0.18)' : 'rgba(255, 255, 255, 0.2)',
    },

    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
      xxxl: 64,
    },

    borderRadius: {
      none: 0,
      xs: 4,
      sm: 8,
      md: 12,
      lg: 16,
      xl: 20,
      xxl: 24,
      round: 50,
      full: 9999,
    },

    elevation: {
      none: 0,
      sm: 2,
      md: 4,
      lg: 8,
      xl: 12,
      xxl: 16,
    },

    shadows: {
      none: {
        shadowColor: 'transparent',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0,
        shadowRadius: 0,
        elevation: 0,
      },
      sm: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.18,
        shadowRadius: 1.0,
        elevation: 1,
      },
      md: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        elevation: 4,
      },
      lg: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.30,
        shadowRadius: 4.65,
        elevation: 8,
      },
      xl: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.37,
        shadowRadius: 7.49,
        elevation: 12,
      },
      xxl: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.44,
        shadowRadius: 10.32,
        elevation: 16,
      },
    },

    typography: {
      // Display styles
      displayLarge: {
        fontSize: 57,
        lineHeight: 64,
        fontWeight: '400',
        letterSpacing: -0.25,
      },
      displayMedium: {
        fontSize: 45,
        lineHeight: 52,
        fontWeight: '400',
        letterSpacing: 0,
      },
      displaySmall: {
        fontSize: 36,
        lineHeight: 44,
        fontWeight: '400',
        letterSpacing: 0,
      },

      // Headline styles
      headlineLarge: {
        fontSize: 32,
        lineHeight: 40,
        fontWeight: '400',
        letterSpacing: 0,
      },
      headlineMedium: {
        fontSize: 28,
        lineHeight: 36,
        fontWeight: '400',
        letterSpacing: 0,
      },
      headlineSmall: {
        fontSize: 24,
        lineHeight: 32,
        fontWeight: '400',
        letterSpacing: 0,
      },

      // Title styles
      titleLarge: {
        fontSize: 22,
        lineHeight: 28,
        fontWeight: '400',
        letterSpacing: 0,
      },
      titleMedium: {
        fontSize: 16,
        lineHeight: 24,
        fontWeight: '500',
        letterSpacing: 0.15,
      },
      titleSmall: {
        fontSize: 14,
        lineHeight: 20,
        fontWeight: '500',
        letterSpacing: 0.1,
      },

      // Body styles
      bodyLarge: {
        fontSize: 16,
        lineHeight: 24,
        fontWeight: '400',
        letterSpacing: 0.5,
      },
      bodyMedium: {
        fontSize: 14,
        lineHeight: 20,
        fontWeight: '400',
        letterSpacing: 0.25,
      },
      bodySmall: {
        fontSize: 12,
        lineHeight: 16,
        fontWeight: '400',
        letterSpacing: 0.4,
      },

      // Label styles
      labelLarge: {
        fontSize: 14,
        lineHeight: 20,
        fontWeight: '500',
        letterSpacing: 0.1,
      },
      labelMedium: {
        fontSize: 12,
        lineHeight: 16,
        fontWeight: '500',
        letterSpacing: 0.5,
      },
      labelSmall: {
        fontSize: 11,
        lineHeight: 16,
        fontWeight: '500',
        letterSpacing: 0.5,
      },
    },

    animations: {
      // Duration
      duration: {
        short: 150,
        medium: 300,
        long: 500,
        extraLong: 700,
      },

      // Easing curves
      easing: {
        standard: 'cubic-bezier(0.2, 0.0, 0, 1.0)',
        decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1.0)',
        accelerate: 'cubic-bezier(0.4, 0.0, 1.0, 1.0)',
        emphasize: 'cubic-bezier(0.2, 0.0, 0, 1.0)',
      },

      // Spring configs
      spring: {
        gentle: {
          damping: 15,
          stiffness: 120,
          mass: 1,
        },
        bouncy: {
          damping: 10,
          stiffness: 150,
          mass: 1,
        },
        snappy: {
          damping: 20,
          stiffness: 300,
          mass: 1,
        },
      },
    },
  };
};

// Export theme instances
export const lightTheme = createTheme('light');
export const darkTheme = createTheme('dark');

// Default theme (light)
export const theme = lightTheme;

// Theme utilities
export const getTheme = (mode) => createTheme(mode);
export const getCurrentTheme = () => createTheme(getSystemTheme());

// Shadow presets for different elevations
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.37,
    shadowRadius: 7.49,
    elevation: 12,
  },
  xxl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
    elevation: 16,
  },
};

// Glassmorphism utility
export const createGlassmorphism = (themeObj, intensity = 0.25) => ({
  backgroundColor: `rgba(255, 255, 255, ${intensity})`,
  borderWidth: 1,
  borderColor: `rgba(255, 255, 255, ${intensity * 0.7})`,
  backdropFilter: 'blur(10px)',
  ...themeObj.shadows.md,
});

// Animation presets
export const animationPresets = {
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  slideInUp: {
    from: { opacity: 0, translateY: 50 },
    to: { opacity: 1, translateY: 0 },
  },
  slideInDown: {
    from: { opacity: 0, translateY: -50 },
    to: { opacity: 1, translateY: 0 },
  },
  slideInLeft: {
    from: { opacity: 0, translateX: -50 },
    to: { opacity: 1, translateX: 0 },
  },
  slideInRight: {
    from: { opacity: 0, translateX: 50 },
    to: { opacity: 1, translateX: 0 },
  },
  scaleIn: {
    from: { opacity: 0, scale: 0.8 },
    to: { opacity: 1, scale: 1 },
  },
  bounce: {
    0: { scale: 1 },
    0.5: { scale: 1.05 },
    1: { scale: 1 },
  },
};

export const commonStyles = {
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  spaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginVertical: theme.spacing.sm,
    ...theme.shadows.md,
  },
  
  button: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  
  buttonText: {
    ...theme.typography.button,
    color: theme.colors.onPrimary,
  },
  
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  
  secondaryButtonText: {
    ...theme.typography.button,
    color: theme.colors.primary,
  },
  
  input: {
    borderWidth: 1,
    borderColor: theme.colors.gray300,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    fontSize: 16,
    backgroundColor: theme.colors.surface,
    minHeight: 48,
  },
  
  inputFocused: {
    borderColor: theme.colors.primary,
    borderWidth: 2,
  },
  
  label: {
    ...theme.typography.body2,
    color: theme.colors.gray700,
    marginBottom: theme.spacing.xs,
    fontWeight: '600',
  },
  
  errorText: {
    ...theme.typography.caption,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  },
  
  divider: {
    height: 1,
    backgroundColor: theme.colors.gray200,
    marginVertical: theme.spacing.sm,
  },
  
  screenPadding: {
    paddingHorizontal: theme.spacing.md,
  },
  
  sectionTitle: {
    ...theme.typography.h5,
    color: theme.colors.onBackground,
    marginBottom: theme.spacing.sm,
  },
  
  nutritionBar: {
    height: 8,
    backgroundColor: theme.colors.gray200,
    borderRadius: theme.borderRadius.sm,
    overflow: 'hidden',
  },
  
  nutritionBarFill: {
    height: '100%',
    borderRadius: theme.borderRadius.sm,
  },
  
  badge: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.round,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    alignSelf: 'flex-start',
  },
  
  badgeText: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    fontWeight: '600',
  },
  
  fab: {
    position: 'absolute',
    bottom: theme.spacing.lg,
    right: theme.spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    ...theme.shadows.lg,
  },
  
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
  
  emptyStateText: {
    ...theme.typography.body1,
    color: theme.colors.gray600,
    textAlign: 'center',
    marginTop: theme.spacing.md,
  },
};

export default theme;
