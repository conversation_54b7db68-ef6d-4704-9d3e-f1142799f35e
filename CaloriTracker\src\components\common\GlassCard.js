import React from 'react';
import { View, StyleSheet } from 'react-native';
import { BlurView } from '@react-native-community/blur';
import { theme } from '../../theme/theme';

/**
 * Glassmorphism Card Component
 */
const GlassCard = ({
  children,
  style,
  blurType = 'light',
  blurAmount = 10,
  intensity = 0.25,
  borderRadius = theme.borderRadius.lg,
  padding = theme.spacing.md,
  margin = theme.spacing.sm,
  elevation = 'md',
  ...props
}) => {
  const glassmorphismStyle = {
    backgroundColor: `rgba(255, 255, 255, ${intensity})`,
    borderWidth: 1,
    borderColor: `rgba(255, 255, 255, ${intensity * 0.7})`,
    borderRadius,
    padding,
    margin,
    ...theme.shadows[elevation],
  };

  return (
    <View style={[styles.container, glassmorphismStyle, style]} {...props}>
      <BlurView
        style={styles.blur}
        blurType={blurType}
        blurAmount={blurAmount}
        reducedTransparencyFallbackColor="white"
      />
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  blur: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  content: {
    position: 'relative',
    zIndex: 1,
  },
});

export default GlassCard;
