{"version": 3, "names": ["ReanimatedError", "<PERSON><PERSON><PERSON><PERSON>", "value", "endsWith", "isNaN", "Number", "parseBoxShadowString", "rawBoxShadows", "result", "rawBoxShadow", "split", "map", "bS", "trim", "filter", "boxShadow", "offsetX", "offsetY", "keywordDetectedAfterLength", "lengthCount", "args", "arg", "blurRadius", "spreadDistance", "inset", "color", "push", "parse<PERSON><PERSON>th", "length", "argsWithUnitsRegex", "match", "exec", "processBoxShadow", "props", "boxShadowList", "replace", "Array", "isArray", "JSON", "stringify", "parsedBoxShadow"], "sourceRoot": "../../src", "sources": ["processBoxShadow.ts"], "mappings": "AAAA;AACA;AACA;AACA,YAAY;;AAEZ;AAIA,SAASA,eAAe,QAAQ,aAAU;AAE1C,MAAMC,QAAQ,GAAIC,KAAa,IAAK;EAClC,SAAS;;EACT,OAAOA,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACH,KAAK,CAAC,CAAC;AACtD,CAAC;AAED,SAASI,oBAAoBA,CAACC,aAAqB,EAAyB;EAC1E,SAAS;;EACT,MAAMC,MAA6B,GAAG,EAAE;EAExC,KAAK,MAAMC,YAAY,IAAIF,aAAa,CACrCG,KAAK,CAAC,eAAe,CAAC,CAAC;EAAA,CACvBC,GAAG,CAAEC,EAAE,IAAKA,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,CACtBC,MAAM,CAAEF,EAAE,IAAKA,EAAE,KAAK,EAAE,CAAC,EAAE;IAC5B,MAAMG,SAAyB,GAAG;MAChCC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;IACD,IAAID,OAA+B,GAAG,IAAI;IAC1C,IAAIC,OAA+B,GAAG,IAAI;IAC1C,IAAIC,0BAA0B,GAAG,KAAK;IAEtC,IAAIC,WAAW,GAAG,CAAC;;IAEnB;IACA,MAAMC,IAAI,GAAGX,YAAY,CAACC,KAAK,CAAC,gBAAgB,CAAC;IACjD,KAAK,MAAMW,GAAG,IAAID,IAAI,EAAE;MACtB,IAAInB,QAAQ,CAACoB,GAAG,CAAC,EAAE;QACjB,QAAQF,WAAW;UACjB,KAAK,CAAC;YACJH,OAAO,GAAGK,GAAG;YACbF,WAAW,EAAE;YACb;UACF,KAAK,CAAC;YACJ,IAAID,0BAA0B,EAAE;cAC9B,OAAO,EAAE;YACX;YACAD,OAAO,GAAGI,GAAG;YACbF,WAAW,EAAE;YACb;UACF,KAAK,CAAC;YACJ,IAAID,0BAA0B,EAAE;cAC9B,OAAO,EAAE;YACX;YACAH,SAAS,CAACO,UAAU,GAAGD,GAAG;YAC1BF,WAAW,EAAE;YACb;UACF,KAAK,CAAC;YACJ,IAAID,0BAA0B,EAAE;cAC9B,OAAO,EAAE;YACX;YACAH,SAAS,CAACQ,cAAc,GAAGF,GAAG;YAC9BF,WAAW,EAAE;YACb;UACF;YACE,OAAO,EAAE;QACb;MACF,CAAC,MAAM,IAAIE,GAAG,KAAK,OAAO,EAAE;QAC1B,IAAIN,SAAS,CAACS,KAAK,EAAE;UACnB,OAAO,EAAE;QACX;QACA,IAAIR,OAAO,KAAK,IAAI,EAAE;UACpBE,0BAA0B,GAAG,IAAI;QACnC;QACAH,SAAS,CAACS,KAAK,GAAG,IAAI;QACtB;MACF,CAAC,MAAM;QACL,IAAIT,SAAS,CAACU,KAAK,EAAE;UACnB,OAAO,EAAE;QACX;QACA,IAAIT,OAAO,IAAI,IAAI,EAAE;UACnBE,0BAA0B,GAAG,IAAI;QACnC;QACAH,SAAS,CAACU,KAAK,GAAGJ,GAAG;QACrB;MACF;IACF;IAEA,IAAIL,OAAO,KAAK,IAAI,IAAIC,OAAO,KAAK,IAAI,EAAE;MACxC,OAAO,EAAE;IACX;IAEAF,SAAS,CAACC,OAAO,GAAGA,OAAO;IAC3BD,SAAS,CAACE,OAAO,GAAGA,OAAO;IAE3BT,MAAM,CAACkB,IAAI,CAACX,SAAS,CAAC;EACxB;EACA,OAAOP,MAAM;AACf;AAEA,SAASmB,WAAWA,CAACC,MAAc,EAAiB;EAClD,SAAS;;EACT;EACA,MAAMC,kBAAkB,GAAG,+BAA+B;EAC1D,MAAMC,KAAK,GAAGD,kBAAkB,CAACE,IAAI,CAACH,MAAM,CAAC;EAE7C,IAAI,CAACE,KAAK,IAAI,CAAC7B,QAAQ,CAAC2B,MAAM,CAAC,EAAE;IAC/B,OAAO,IAAI;EACb;EAEA,OAAOvB,MAAM,CAACyB,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB;AAWA,OAAO,SAASE,gBAAgBA,CAACC,KAAiB,EAAE;EAClD,SAAS;;EACT,MAAMzB,MAA8B,GAAG,EAAE;EAEzC,MAAMD,aAAa,GAAG0B,KAAK,CAAClB,SAAS;EAErC,IAAIR,aAAa,KAAK,IAAI,EAAE;IAC1B,OAAOC,MAAM;EACf;EAEA,IAAI0B,aAAoC;EAExC,IAAI,OAAO3B,aAAa,KAAK,QAAQ,EAAE;IACrC2B,aAAa,GAAG5B,oBAAoB,CAACC,aAAa,CAAC4B,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;EACzE,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAC9B,aAAa,CAAC,EAAE;IACvC2B,aAAa,GAAG3B,aAAa;EAC/B,CAAC,MAAM;IACL,MAAM,IAAIP,eAAe,CACvB,8EAA8EsC,IAAI,CAACC,SAAS,CAAChC,aAAa,CAAC,EAC7G,CAAC;EACH;EAEA,KAAK,MAAME,YAAY,IAAIyB,aAAa,EAAE;IACxC,MAAMM,eAAgC,GAAG;MACvCxB,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;IAED,IAAIf,KAAK;IACT,KAAK,MAAMmB,GAAG,IAAIZ,YAAY,EAAE;MAC9B,QAAQY,GAAG;QACT,KAAK,SAAS;UACZnB,KAAK,GACH,OAAOO,YAAY,CAACO,OAAO,KAAK,QAAQ,GACpCW,WAAW,CAAClB,YAAY,CAACO,OAAO,CAAC,GACjCP,YAAY,CAACO,OAAO;UAC1B,IAAId,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,EAAE;UACX;UAEAsC,eAAe,CAACxB,OAAO,GAAGd,KAAK;UAC/B;QACF,KAAK,SAAS;UACZA,KAAK,GACH,OAAOO,YAAY,CAACQ,OAAO,KAAK,QAAQ,GACpCU,WAAW,CAAClB,YAAY,CAACQ,OAAO,CAAC,GACjCR,YAAY,CAACQ,OAAO;UAC1B,IAAIf,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,EAAE;UACX;UAEAsC,eAAe,CAACvB,OAAO,GAAGf,KAAK;UAC/B;QACF,KAAK,gBAAgB;UACnBA,KAAK,GACH,OAAOO,YAAY,CAACc,cAAc,KAAK,QAAQ,GAC3CI,WAAW,CAAClB,YAAY,CAACc,cAAc,CAAC,GACxCd,YAAY,CAACc,cAAc;UACjC,IAAIrB,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,EAAE;UACX;UAEAsC,eAAe,CAACjB,cAAc,GAAGrB,KAAK;UACtC;QACF,KAAK,YAAY;UACfA,KAAK,GACH,OAAOO,YAAY,CAACa,UAAU,KAAK,QAAQ,GACvCK,WAAW,CAAClB,YAAY,CAACa,UAAU,CAAC,GACnCb,YAAY,CAACa,UAAqB;UACzC,IAAIpB,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;YAC/B,OAAO,EAAE;UACX;UAEAsC,eAAe,CAAClB,UAAU,GAAGpB,KAAK;UAClC;QACF,KAAK,OAAO;UACVsC,eAAe,CAACf,KAAK,GAAGhB,YAAY,CAACgB,KAAK;UAC1C;QACF,KAAK,OAAO;UACVe,eAAe,CAAChB,KAAK,GAAGf,YAAY,CAACe,KAAK;MAC9C;IACF;IACAhB,MAAM,CAACkB,IAAI,CAACc,eAAe,CAAC;EAC9B;EACAP,KAAK,CAAClB,SAAS,GAAGP,MAAM;AAC1B", "ignoreList": []}