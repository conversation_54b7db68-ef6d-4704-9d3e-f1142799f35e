{"version": 3, "names": ["isWorkletFunction", "ReanimatedError", "registerWorkletStackDetails", "logger", "jsVersion", "shouldBeUseWeb", "shareableMappingCache", "shareableMappingFlag", "WorkletsModule", "SHOULD_BE_USE_WEB", "MAGIC_KEY", "isHostObject", "value", "isPlainJSObject", "object", "Object", "getPrototypeOf", "prototype", "getFromCache", "cached", "get", "INACCESSIBLE_OBJECT", "__init", "Proxy", "_", "prop", "String", "set", "VALID_ARRAY_VIEWS_NAMES", "DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD", "processedObjectAtThresholdDepth", "makeShareableCloneRecursiveWeb", "makeShareableCloneRecursiveNative", "shouldPersistRemote", "depth", "detectCyclicObject", "isObject", "isFunction", "clonePrimitive", "undefined", "Array", "isArray", "cloneArray", "cloneRemoteFunction", "cloneHostObject", "__workletContextObjectFactory", "cloneContextObject", "cloneWorklet", "clonePlainJSObject", "RegExp", "cloneRegExp", "Error", "cloneError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "cloneArrayBufferView", "inaccessibleObject", "makeShareableCloneRecursive", "makeShareableClone", "clonedElements", "map", "element", "clone", "freezeObjectInDev", "__DEV__", "babelVersion", "__initData", "version", "getWorkletCode", "__workletHash", "__stackDetails", "clonedProps", "key", "entries", "workletContextObjectFactory", "handle", "pattern", "source", "flags", "name", "message", "stack", "error", "buffer", "typeName", "constructor", "includes", "global", "WORKLET_CODE_THRESHOLD", "code", "length", "substring", "isRemoteFunction", "__remoteFunction", "for<PERSON>ach", "descriptor", "getOwnPropertyDescriptor", "configurable", "defineProperty", "warn", "preventExtensions", "makeShareableCloneOnUIRecursive", "cloneRecursive", "_makeShareableClone", "toAdapt", "makeShareableJS", "makeShareableNative", "makeShareable"], "sourceRoot": "../../src", "sources": ["shareables.ts"], "mappings": "AAAA,YAAY;;AAOZ,SAASA,iBAAiB,QAAQ,kBAAe;AACjD,SAASC,eAAe,EAAEC,2BAA2B,QAAQ,aAAU;AACvE,SAASC,MAAM,QAAQ,mBAAU;AACjC,SAASC,SAAS,QAAQ,kCAA+B;AACzD,SAASC,cAAc,QAAQ,sBAAmB;AAClD,SACEC,qBAAqB,EACrBC,oBAAoB,QACf,4BAAyB;AAChC,SAASC,cAAc,QAAQ,qBAAY;;AAE3C;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGJ,cAAc,CAAC,CAAC;AAE1C,MAAMK,SAAS,GAAG,sBAAsB;AAExC,SAASC,YAAYA,CAACC,KAA0B,EAAE;EAChD,SAAS;;EACT;EACA;EACA;EACA;EACA,OAAOF,SAAS,IAAIE,KAAK;AAC3B;AAEA,SAASC,eAAeA,CAACC,MAAc,EAAqC;EAC1E,OAAOC,MAAM,CAACC,cAAc,CAACF,MAAM,CAAC,KAAKC,MAAM,CAACE,SAAS;AAC3D;AAEA,SAASC,YAAYA,CAACN,KAAa,EAAE;EACnC,MAAMO,MAAM,GAAGb,qBAAqB,CAACc,GAAG,CAACR,KAAK,CAAC;EAC/C,IAAIO,MAAM,KAAKZ,oBAAoB,EAAE;IACnC;IACA,OAAOK,KAAK;EACd;EACA,OAAOO,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,mBAAmB,GAAG;EAC1BC,MAAM,EAAEA,CAAA,KAAM;IACZ,SAAS;;IACT,OAAO,IAAIC,KAAK,CACd,CAAC,CAAC,EACF;MACEH,GAAG,EAAEA,CAACI,CAAU,EAAEC,IAAqB,KAAK;QAC1C,IACEA,IAAI,KAAK,0BAA0B,IACnCA,IAAI,KAAK,kBAAkB,EAC3B;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,OAAO,KAAK;QACd;QACA,MAAM,IAAIxB,eAAe,CACvB,+BAA+ByB,MAAM,CACnCD,IACF,CAAC,yDACH,CAAC;MACH,CAAC;MACDE,GAAG,EAAEA,CAAA,KAAM;QACT,MAAM,IAAI1B,eAAe,CACvB,sEACF,CAAC;MACH;IACF,CACF,CAAC;EACH;AACF,CAAC;AAED,MAAM2B,uBAAuB,GAAG,CAC9B,WAAW,EACX,YAAY,EACZ,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,UAAU,CACX;AAED,MAAMC,oCAAoC,GAAG,EAAE;AAC/C;AACA;AACA,IAAIC,+BAAwC;AAE5C,SAASC,8BAA8BA,CAAInB,KAAQ,EAAmB;EACpE,OAAOA,KAAK;AACd;AAEA,SAASoB,iCAAiCA,CACxCpB,KAAQ,EACRqB,mBAAmB,GAAG,KAAK,EAC3BC,KAAK,GAAG,CAAC,EACQ;EACjBC,kBAAkB,CAACvB,KAAK,EAAEsB,KAAK,CAAC;EAEhC,MAAME,QAAQ,GAAG,OAAOxB,KAAK,KAAK,QAAQ;EAC1C,MAAMyB,UAAU,GAAG,OAAOzB,KAAK,KAAK,UAAU;EAE9C,IAAK,CAACwB,QAAQ,IAAI,CAACC,UAAU,IAAKzB,KAAK,KAAK,IAAI,EAAE;IAChD,OAAO0B,cAAc,CAAC1B,KAAK,EAAEqB,mBAAmB,CAAC;EACnD;EAEA,MAAMd,MAAM,GAAGD,YAAY,CAACN,KAAK,CAAC;EAClC,IAAIO,MAAM,KAAKoB,SAAS,EAAE;IACxB,OAAOpB,MAAM;EACf;EAEA,IAAIqB,KAAK,CAACC,OAAO,CAAC7B,KAAK,CAAC,EAAE;IACxB,OAAO8B,UAAU,CAAC9B,KAAK,EAAEqB,mBAAmB,EAAEC,KAAK,CAAC;EACtD;EACA,IAAIG,UAAU,IAAI,CAACrC,iBAAiB,CAACY,KAAK,CAAC,EAAE;IAC3C,OAAO+B,mBAAmB,CAAC/B,KAAK,EAAEqB,mBAAmB,CAAC;EACxD;EACA,IAAItB,YAAY,CAACC,KAAK,CAAC,EAAE;IACvB,OAAOgC,eAAe,CAAChC,KAAK,EAAEqB,mBAAmB,CAAC;EACpD;EACA,IAAIpB,eAAe,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACiC,6BAA6B,EAAE;IACjE,OAAOC,kBAAkB,CAAClC,KAAK,CAAC;EAClC;EACA,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC,IAAIyB,UAAU,KAAKrC,iBAAiB,CAACY,KAAK,CAAC,EAAE;IACtE,OAAOmC,YAAY,CAACnC,KAAK,EAAEqB,mBAAmB,EAAEC,KAAK,CAAC;EACxD;EACA,IAAIrB,eAAe,CAACD,KAAK,CAAC,IAAIyB,UAAU,EAAE;IACxC,OAAOW,kBAAkB,CAACpC,KAAK,EAAEqB,mBAAmB,EAAEC,KAAK,CAAC;EAC9D;EACA,IAAItB,KAAK,YAAYqC,MAAM,EAAE;IAC3B,OAAOC,WAAW,CAACtC,KAAK,CAAC;EAC3B;EACA,IAAIA,KAAK,YAAYuC,KAAK,EAAE;IAC1B,OAAOC,UAAU,CAACxC,KAAK,CAAC;EAC1B;EACA,IAAIA,KAAK,YAAYyC,WAAW,EAAE;IAChC,OAAOC,gBAAgB,CAAC1C,KAAK,EAAEqB,mBAAmB,CAAC;EACrD;EACA,IAAIoB,WAAW,CAACE,MAAM,CAAC3C,KAAK,CAAC,EAAE;IAC7B;IACA,OAAO4C,oBAAoB,CAAC5C,KAAK,CAAC;EACpC;EACA,OAAO6C,kBAAkB,CAAC7C,KAAK,CAAC;AAClC;AAMA,OAAO,MAAM8C,2BAA+C,GAAGjD,iBAAiB,GAC5EsB,8BAA8B,GAC9BC,iCAAiC;AAErC,SAASG,kBAAkBA,CAACvB,KAAc,EAAEsB,KAAa,EAAE;EACzD,IAAIA,KAAK,IAAIL,oCAAoC,EAAE;IACjD;IACA;IACA;IACA;IACA;IACA,IAAIK,KAAK,KAAKL,oCAAoC,EAAE;MAClDC,+BAA+B,GAAGlB,KAAK;IACzC,CAAC,MAAM,IAAIA,KAAK,KAAKkB,+BAA+B,EAAE;MACpD,MAAM,IAAI7B,eAAe,CACvB,0EACF,CAAC;IACH;EACF,CAAC,MAAM;IACL6B,+BAA+B,GAAGS,SAAS;EAC7C;AACF;AAEA,SAASD,cAAcA,CACrB1B,KAAQ,EACRqB,mBAA4B,EACX;EACjB,OAAOzB,cAAc,CAACmD,kBAAkB,CAAC/C,KAAK,EAAEqB,mBAAmB,CAAC;AACtE;AAEA,SAASS,UAAUA,CACjB9B,KAAQ,EACRqB,mBAA4B,EAC5BC,KAAa,EACI;EACjB,MAAM0B,cAAc,GAAGhD,KAAK,CAACiD,GAAG,CAAEC,OAAO,IACvCJ,2BAA2B,CAACI,OAAO,EAAE7B,mBAAmB,EAAEC,KAAK,GAAG,CAAC,CACrE,CAAC;EACD,MAAM6B,KAAK,GAAGvD,cAAc,CAACmD,kBAAkB,CAC7CC,cAAc,EACd3B,mBAAmB,EACnBrB,KACF,CAAoB;EACpBN,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEmD,KAAK,CAAC;EACvCzD,qBAAqB,CAACqB,GAAG,CAACoC,KAAK,CAAC;EAEhCC,iBAAiB,CAACpD,KAAK,CAAC;EACxB,OAAOmD,KAAK;AACd;AAEA,SAASpB,mBAAmBA,CAC1B/B,KAAQ,EACRqB,mBAA4B,EACX;EACjB,MAAM8B,KAAK,GAAGvD,cAAc,CAACmD,kBAAkB,CAC7C/C,KAAK,EACLqB,mBAAmB,EACnBrB,KACF,CAAC;EACDN,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEmD,KAAK,CAAC;EACvCzD,qBAAqB,CAACqB,GAAG,CAACoC,KAAK,CAAC;EAEhCC,iBAAiB,CAACpD,KAAK,CAAC;EACxB,OAAOmD,KAAK;AACd;AAEA,SAASnB,eAAeA,CACtBhC,KAAQ,EACRqB,mBAA4B,EACX;EACjB;EACA;EACA;EACA,MAAM8B,KAAK,GAAGvD,cAAc,CAACmD,kBAAkB,CAC7C/C,KAAK,EACLqB,mBAAmB,EACnBrB,KACF,CAAC;EACDN,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEmD,KAAK,CAAC;EACvCzD,qBAAqB,CAACqB,GAAG,CAACoC,KAAK,CAAC;EAEhC,OAAOA,KAAK;AACd;AAEA,SAAShB,YAAYA,CACnBnC,KAAQ,EACRqB,mBAA4B,EAC5BC,KAAa,EACI;EACjB,IAAI+B,OAAO,EAAE;IACX,MAAMC,YAAY,GAAItD,KAAK,CAAwBuD,UAAU,CAACC,OAAO;IACrE,IAAIF,YAAY,KAAK3B,SAAS,IAAI2B,YAAY,KAAK9D,SAAS,EAAE;MAC5D,MAAM,IAAIH,eAAe,CAAC,8FAA8FG,SAAS,QAAQ8D,YAAY;AAC3J;AACA,wBAAwBG,cAAc,CAACzD,KAAK,CAAC,IAAI,CAAC;IAC9C;IACAV,2BAA2B,CACzBU,KAAK,CAAC0D,aAAa,EAClB1D,KAAK,CAAwB2D,cAChC,CAAC;EACH;EACA,IAAK3D,KAAK,CAAwB2D,cAAc,EAAE;IAChD;IACA;IACA;IACA;IACA,OAAQ3D,KAAK,CAAwB2D,cAAc;EACrD;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,WAAoC,GAAG,CAAC,CAAC;EAC/CA,WAAW,CAACL,UAAU,GAAGT,2BAA2B,CAClD9C,KAAK,CAACuD,UAAU,EAChB,IAAI,EACJjC,KAAK,GAAG,CACV,CAAC;EAED,KAAK,MAAM,CAACuC,GAAG,EAAEX,OAAO,CAAC,IAAI/C,MAAM,CAAC2D,OAAO,CAAC9D,KAAK,CAAC,EAAE;IAClD,IAAI6D,GAAG,KAAK,YAAY,IAAID,WAAW,CAACL,UAAU,KAAK5B,SAAS,EAAE;MAChE;IACF;IACAiC,WAAW,CAACC,GAAG,CAAC,GAAGf,2BAA2B,CAC5CI,OAAO,EACP7B,mBAAmB,EACnBC,KAAK,GAAG,CACV,CAAC;EACH;EACA,MAAM6B,KAAK,GAAGvD,cAAc,CAACmD,kBAAkB,CAC7Ca,WAAW;EACX;EACA,IAAI,EACJ5D,KACF,CAAoB;EACpBN,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEmD,KAAK,CAAC;EACvCzD,qBAAqB,CAACqB,GAAG,CAACoC,KAAK,CAAC;EAEhCC,iBAAiB,CAACpD,KAAK,CAAC;EACxB,OAAOmD,KAAK;AACd;AAEA,SAASjB,kBAAkBA,CAAmBlC,KAAQ,EAAmB;EACvE,MAAM+D,2BAA2B,GAAI/D,KAAK,CACvCiC,6BAAwC;EAC3C,MAAM+B,MAAM,GAAGlB,2BAA2B,CAAC;IACzCpC,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,OAAOqD,2BAA2B,CAAC,CAAC;IACtC;EACF,CAAC,CAAC;EACFrE,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEgE,MAAM,CAAC;EACxC,OAAOA,MAAM;AACf;AAEA,SAAS5B,kBAAkBA,CACzBpC,KAAQ,EACRqB,mBAA4B,EAC5BC,KAAa,EACI;EACjB,MAAMsC,WAAoC,GAAG,CAAC,CAAC;EAC/C,KAAK,MAAM,CAACC,GAAG,EAAEX,OAAO,CAAC,IAAI/C,MAAM,CAAC2D,OAAO,CAAC9D,KAAK,CAAC,EAAE;IAClD,IAAI6D,GAAG,KAAK,YAAY,IAAID,WAAW,CAACL,UAAU,KAAK5B,SAAS,EAAE;MAChE;IACF;IACAiC,WAAW,CAACC,GAAG,CAAC,GAAGf,2BAA2B,CAC5CI,OAAO,EACP7B,mBAAmB,EACnBC,KAAK,GAAG,CACV,CAAC;EACH;EACA,MAAM6B,KAAK,GAAGvD,cAAc,CAACmD,kBAAkB,CAC7Ca,WAAW,EACXvC,mBAAmB,EACnBrB,KACF,CAAoB;EACpBN,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEmD,KAAK,CAAC;EACvCzD,qBAAqB,CAACqB,GAAG,CAACoC,KAAK,CAAC;EAEhCC,iBAAiB,CAACpD,KAAK,CAAC;EACxB,OAAOmD,KAAK;AACd;AAEA,SAASb,WAAWA,CAAmBtC,KAAQ,EAAmB;EAChE,MAAMiE,OAAO,GAAGjE,KAAK,CAACkE,MAAM;EAC5B,MAAMC,KAAK,GAAGnE,KAAK,CAACmE,KAAK;EACzB,MAAMH,MAAM,GAAGlB,2BAA2B,CAAC;IACzCpC,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,OAAO,IAAI2B,MAAM,CAAC4B,OAAO,EAAEE,KAAK,CAAC;IACnC;EACF,CAAC,CAA+B;EAChCzE,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEgE,MAAM,CAAC;EAExC,OAAOA,MAAM;AACf;AAEA,SAASxB,UAAUA,CAAkBxC,KAAQ,EAAmB;EAC9D,MAAM;IAAEoE,IAAI;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGtE,KAAK;EACtC,MAAMgE,MAAM,GAAGlB,2BAA2B,CAAC;IACzCpC,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT;MACA,MAAM6D,KAAK,GAAG,IAAIhC,KAAK,CAAC,CAAC;MACzBgC,KAAK,CAACH,IAAI,GAAGA,IAAI;MACjBG,KAAK,CAACF,OAAO,GAAGA,OAAO;MACvBE,KAAK,CAACD,KAAK,GAAGA,KAAK;MACnB,OAAOC,KAAK;IACd;EACF,CAAC,CAAC;EACF7E,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEgE,MAAM,CAAC;EACxC,OAAOA,MAAM;AACf;AAEA,SAAStB,gBAAgBA,CACvB1C,KAAQ,EACRqB,mBAA4B,EACX;EACjB,MAAM8B,KAAK,GAAGvD,cAAc,CAACmD,kBAAkB,CAC7C/C,KAAK,EACLqB,mBAAmB,EACnBrB,KACF,CAAC;EACDN,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEmD,KAAK,CAAC;EACvCzD,qBAAqB,CAACqB,GAAG,CAACoC,KAAK,CAAC;EAEhC,OAAOA,KAAK;AACd;AAEA,SAASP,oBAAoBA,CAC3B5C,KAAQ,EACS;EACjB,MAAMwE,MAAM,GAAGxE,KAAK,CAACwE,MAAM;EAC3B,MAAMC,QAAQ,GAAGzE,KAAK,CAAC0E,WAAW,CAACN,IAAI;EACvC,MAAMJ,MAAM,GAAGlB,2BAA2B,CAAC;IACzCpC,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,IAAI,CAACM,uBAAuB,CAAC2D,QAAQ,CAACF,QAAQ,CAAC,EAAE;QAC/C,MAAM,IAAIpF,eAAe,CACvB,0CAA0CoF,QAAQ,KACpD,CAAC;MACH;MACA,MAAMC,WAAW,GAAGE,MAAM,CAACH,QAAQ,CAAwB;MAC3D,IAAIC,WAAW,KAAK/C,SAAS,EAAE;QAC7B,MAAM,IAAItC,eAAe,CACvB,kCAAkCoF,QAAQ,eAC5C,CAAC;MACH;MACA,OAAO,IAAIC,WAAW,CAACF,MAAM,CAAC;IAChC;EACF,CAAC,CAA+B;EAChC9E,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEgE,MAAM,CAAC;EAExC,OAAOA,MAAM;AACf;AAEA,SAASnB,kBAAkBA,CAAmB7C,KAAQ,EAAmB;EACvE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMmD,KAAK,GAAGL,2BAA2B,CAAIrC,mBAAwB,CAAC;EACtEf,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEmD,KAAK,CAAC;EACvC,OAAOA,KAAK;AACd;AAEA,MAAM0B,sBAAsB,GAAG,GAAG;AAElC,SAASpB,cAAcA,CAACzD,KAAsB,EAAE;EAC9C,MAAM8E,IAAI,GAAG9E,KAAK,EAAEuD,UAAU,EAAEuB,IAAI;EACpC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,SAAS;EAClB;EACA,IAAIA,IAAI,CAACC,MAAM,GAAGF,sBAAsB,EAAE;IACxC,OAAO,GAAGC,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEH,sBAAsB,CAAC,KAAK;EAC1D;EACA,OAAOC,IAAI;AACb;AAMA,SAASG,gBAAgBA,CAAIjF,KAE5B,EAA8B;EAC7B,SAAS;;EACT,OAAO,CAAC,CAACA,KAAK,CAACkF,gBAAgB;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9B,iBAAiBA,CAAmBpD,KAAQ,EAAE;EACrD,IAAI,CAACqD,OAAO,EAAE;IACZ;EACF;EACAlD,MAAM,CAAC2D,OAAO,CAAC9D,KAAK,CAAC,CAACmF,OAAO,CAAC,CAAC,CAACtB,GAAG,EAAEX,OAAO,CAAC,KAAK;IAChD,MAAMkC,UAAU,GAAGjF,MAAM,CAACkF,wBAAwB,CAACrF,KAAK,EAAE6D,GAAG,CAAE;IAC/D,IAAI,CAACuB,UAAU,CAACE,YAAY,EAAE;MAC5B;IACF;IACAnF,MAAM,CAACoF,cAAc,CAACvF,KAAK,EAAE6D,GAAG,EAAE;MAChCrD,GAAGA,CAAA,EAAG;QACJ,OAAO0C,OAAO;MAChB,CAAC;MACDnC,GAAGA,CAAA,EAAG;QACJxB,MAAM,CAACiG,IAAI,CACT,yBAAyB3B,GAAG;AACtC;AACA,kBACQ,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF1D,MAAM,CAACsF,iBAAiB,CAACzF,KAAK,CAAC;AACjC;AAEA,OAAO,SAAS0F,+BAA+BA,CAC7C1F,KAAQ,EACa;EACrB,SAAS;;EACT,IAAIH,iBAAiB,EAAE;IACrB;IACA;IACA,OAAOG,KAAK;EACd;EACA;EACA,SAAS2F,cAAcA,CAAC3F,KAAQ,EAAuB;IACrD,IACG,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAC5C,OAAOA,KAAK,KAAK,UAAU,EAC3B;MACA,IAAID,YAAY,CAACC,KAAK,CAAC,EAAE;QACvB;QACA;QACA,OAAO4E,MAAM,CAACgB,mBAAmB,CAC/B5F,KAAK,EACL2B,SACF,CAAC;MACH;MACA,IAAIsD,gBAAgB,CAAIjF,KAAK,CAAC,EAAE;QAC9B;QACA;QACA;QACA,OAAOA,KAAK,CAACkF,gBAAgB;MAC/B;MACA,IAAItD,KAAK,CAACC,OAAO,CAAC7B,KAAK,CAAC,EAAE;QACxB,OAAO4E,MAAM,CAACgB,mBAAmB,CAC/B5F,KAAK,CAACiD,GAAG,CAAC0C,cAAc,CAAC,EACzBhE,SACF,CAAC;MACH;MACA,MAAMkE,OAA4C,GAAG,CAAC,CAAC;MACvD,KAAK,MAAM,CAAChC,GAAG,EAAEX,OAAO,CAAC,IAAI/C,MAAM,CAAC2D,OAAO,CAAC9D,KAAK,CAAC,EAAE;QAClD6F,OAAO,CAAChC,GAAG,CAAC,GAAG8B,cAAc,CAACzC,OAAO,CAAC;MACxC;MACA,OAAO0B,MAAM,CAACgB,mBAAmB,CAACC,OAAO,EAAE7F,KAAK,CAAC;IACnD;IACA,OAAO4E,MAAM,CAACgB,mBAAmB,CAAC5F,KAAK,EAAE2B,SAAS,CAAC;EACrD;EACA,OAAOgE,cAAc,CAAC3F,KAAK,CAAC;AAC9B;AAEA,SAAS8F,eAAeA,CAAmB9F,KAAQ,EAAK;EACtD,OAAOA,KAAK;AACd;AAEA,SAAS+F,mBAAmBA,CAAmB/F,KAAQ,EAAK;EAC1D,IAAIN,qBAAqB,CAACc,GAAG,CAACR,KAAK,CAAC,EAAE;IACpC,OAAOA,KAAK;EACd;EACA,MAAMgE,MAAM,GAAGlB,2BAA2B,CAAC;IACzCpC,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,OAAOV,KAAK;IACd;EACF,CAAC,CAAC;EACFN,qBAAqB,CAACqB,GAAG,CAACf,KAAK,EAAEgE,MAAM,CAAC;EACxC,OAAOhE,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgG,aAAa,GAAGnG,iBAAiB,GAC1CiG,eAAe,GACfC,mBAAmB", "ignoreList": []}