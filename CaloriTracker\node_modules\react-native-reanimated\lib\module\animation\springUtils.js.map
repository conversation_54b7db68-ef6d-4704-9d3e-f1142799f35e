{"version": 3, "names": ["logger", "checkIfConfigIsValid", "config", "errorMessage", "for<PERSON>ach", "prop", "value", "duration", "clamp", "min", "max", "warn", "bisectRoot", "func", "maxIterations", "ACCURACY", "idx", "current", "Math", "abs", "initialCalculations", "mass", "skipAnimation", "zeta", "omega0", "omega1", "useDuration", "stiffness", "k", "dampingRatio", "sqrt", "damping", "c", "m", "scaleZetaToMatchClamps", "animation", "toValue", "startValue", "toValueNum", "Number", "firstBound", "secondBound", "relativeExtremum1", "undefined", "relativeExtremum2", "newZeta1", "log", "PI", "newZeta2", "zetaSatisfyingClamp", "filter", "x", "calculateNewMassToMatchDuration", "x0", "v0", "restSpeedThreshold", "threshold", "durationForMass", "amplitude", "exp", "criticallyDampedSpringCalculations", "precalculatedValues", "t", "criticallyDampedEnvelope", "criticallyDampedPosition", "criticallyDampedVelocity", "position", "velocity", "underDampedSpringCalculations", "sin1", "sin", "cos1", "cos", "underDampedEnvelope", "underDampedFrag1", "underDampedPosition", "underDampedVelocity", "isAnimationTerminatingCalculation", "isOvershooting", "overshootClamping", "isVelocity", "isDisplacement", "restDisplacementThreshold"], "sourceRoot": "../../../src", "sources": ["animation/springUtils.ts"], "mappings": "AAAA,YAAY;;AAOZ,SAASA,MAAM,QAAQ,oBAAW;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAyBA;AACA;;AA8BA,OAAO,SAASC,oBAAoBA,CAACC,MAA2B,EAAW;EACzE,SAAS;;EACT,IAAIC,YAAY,GAAG,EAAE;EAEnB,CACE,WAAW,EACX,SAAS,EACT,cAAc,EACd,2BAA2B,EAC3B,oBAAoB,EACpB,MAAM,CACP,CACDC,OAAO,CAAEC,IAAI,IAAK;IAClB,MAAMC,KAAK,GAAGJ,MAAM,CAACG,IAAI,CAAC;IAC1B,IAAIC,KAAK,IAAI,CAAC,EAAE;MACdH,YAAY,IAAI,KAAKE,IAAI,qCAAqCC,KAAK,EAAE;IACvE;EACF,CAAC,CAAC;EAEF,IAAIJ,MAAM,CAACK,QAAQ,GAAG,CAAC,EAAE;IACvBJ,YAAY,IAAI,qCAAqCD,MAAM,CAACK,QAAQ,EAAE;EACxE;EAEA,IACEL,MAAM,CAACM,KAAK,EAAEC,GAAG,IACjBP,MAAM,CAACM,KAAK,EAAEE,GAAG,IACjBR,MAAM,CAACM,KAAK,CAACC,GAAG,GAAGP,MAAM,CAACM,KAAK,CAACE,GAAG,EACnC;IACAP,YAAY,IAAI,gEAAgED,MAAM,CAACM,KAAK,CAACC,GAAG,UAAUP,MAAM,CAACM,KAAK,CAACE,GAAG,IAAI;EAChI;EAEA,IAAIP,YAAY,KAAK,EAAE,EAAE;IACvBH,MAAM,CAACW,IAAI,CAAC,uBAAuB,GAAGR,YAAY,CAAC;EACrD;EAEA,OAAOA,YAAY,KAAK,EAAE;AAC5B;;AAEA;AACA,OAAO,SAASS,UAAUA,CAAC;EACzBH,GAAG;EACHC,GAAG;EACHG,IAAI;EACJC,aAAa,GAAG;AAMlB,CAAC,EAAE;EACD,SAAS;;EACT,MAAMC,QAAQ,GAAG,OAAO;EACxB,IAAIC,GAAG,GAAGF,aAAa;EACvB,IAAIG,OAAO,GAAG,CAACP,GAAG,GAAGD,GAAG,IAAI,CAAC;EAC7B,OAAOS,IAAI,CAACC,GAAG,CAACN,IAAI,CAACI,OAAO,CAAC,CAAC,GAAGF,QAAQ,IAAIC,GAAG,GAAG,CAAC,EAAE;IACpDA,GAAG,IAAI,CAAC;IAER,IAAIH,IAAI,CAACI,OAAO,CAAC,GAAG,CAAC,EAAE;MACrBR,GAAG,GAAGQ,OAAO;IACf,CAAC,MAAM;MACLP,GAAG,GAAGO,OAAO;IACf;IACAA,OAAO,GAAG,CAACR,GAAG,GAAGC,GAAG,IAAI,CAAC;EAC3B;EACA,OAAOO,OAAO;AAChB;AAEA,OAAO,SAASG,mBAAmBA,CACjCC,IAAI,GAAG,CAAC,EACRnB,MAA+C,EAK/C;EACA,SAAS;;EAET,IAAIA,MAAM,CAACoB,aAAa,EAAE;IACxB,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EAC1C;EAEA,IAAIvB,MAAM,CAACwB,WAAW,EAAE;IACtB,MAAM;MAAEC,SAAS,EAAEC,CAAC;MAAEC,YAAY,EAAEN;IAAK,CAAC,GAAGrB,MAAM;;IAEnD;AACJ;AACA;AACA;AACA;IACI,MAAMsB,MAAM,GAAGN,IAAI,CAACY,IAAI,CAACF,CAAC,GAAGP,IAAI,CAAC;IAClC,MAAMI,MAAM,GAAGD,MAAM,GAAGN,IAAI,CAACY,IAAI,CAAC,CAAC,GAAGP,IAAI,IAAI,CAAC,CAAC;IAEhD,OAAO;MAAEA,IAAI;MAAEC,MAAM;MAAEC;IAAO,CAAC;EACjC,CAAC,MAAM;IACL,MAAM;MAAEM,OAAO,EAAEC,CAAC;MAAEX,IAAI,EAAEY,CAAC;MAAEN,SAAS,EAAEC;IAAE,CAAC,GAAG1B,MAAM;IAEpD,MAAMqB,IAAI,GAAGS,CAAC,IAAI,CAAC,GAAGd,IAAI,CAACY,IAAI,CAACF,CAAC,GAAGK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,MAAMT,MAAM,GAAGN,IAAI,CAACY,IAAI,CAACF,CAAC,GAAGK,CAAC,CAAC,CAAC,CAAC;IACjC,MAAMR,MAAM,GAAGD,MAAM,GAAGN,IAAI,CAACY,IAAI,CAAC,CAAC,GAAGP,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;;IAElD,OAAO;MAAEA,IAAI;MAAEC,MAAM;MAAEC;IAAO,CAAC;EACjC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,sBAAsBA,CACpCC,SAA0B,EAC1B3B,KAAqC,EAC7B;EACR,SAAS;;EACT,MAAM;IAAEe,IAAI;IAAEa,OAAO;IAAEC;EAAW,CAAC,GAAGF,SAAS;EAC/C,MAAMG,UAAU,GAAGC,MAAM,CAACH,OAAO,CAAC;EAElC,IAAIE,UAAU,KAAKD,UAAU,EAAE;IAC7B,OAAOd,IAAI;EACb;EAEA,MAAM,CAACiB,UAAU,EAAEC,WAAW,CAAC,GAC7BH,UAAU,GAAGD,UAAU,GAAG,CAAC,GACvB,CAAC7B,KAAK,CAACC,GAAG,EAAED,KAAK,CAACE,GAAG,CAAC,GACtB,CAACF,KAAK,CAACE,GAAG,EAAEF,KAAK,CAACC,GAAG,CAAC;;EAE5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,MAAMiC,iBAAiB,GACrBD,WAAW,KAAKE,SAAS,GACrBzB,IAAI,CAACC,GAAG,CAAC,CAACsB,WAAW,GAAGH,UAAU,KAAKA,UAAU,GAAGD,UAAU,CAAC,CAAC,GAChEM,SAAS;EAEf,MAAMC,iBAAiB,GACrBJ,UAAU,KAAKG,SAAS,GACpBzB,IAAI,CAACC,GAAG,CAAC,CAACqB,UAAU,GAAGF,UAAU,KAAKA,UAAU,GAAGD,UAAU,CAAC,CAAC,GAC/DM,SAAS;;EAEf;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,MAAME,QAAQ,GACZH,iBAAiB,KAAKC,SAAS,GAC3BzB,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC4B,GAAG,CAACJ,iBAAiB,CAAC,GAAGxB,IAAI,CAAC6B,EAAE,CAAC,GAC/CJ,SAAS;EAEf,MAAMK,QAAQ,GACZJ,iBAAiB,KAAKD,SAAS,GAC3BzB,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC4B,GAAG,CAACF,iBAAiB,CAAC,IAAI,CAAC,GAAG1B,IAAI,CAAC6B,EAAE,CAAC,CAAC,GACrDJ,SAAS;EAEf,MAAMM,mBAAmB,GAAG,CAACJ,QAAQ,EAAEG,QAAQ,CAAC,CAACE,MAAM,CACpDC,CAAqB,IAAkBA,CAAC,KAAKR,SAChD,CAAC;EACD;EACA;EACA,OAAOzB,IAAI,CAACR,GAAG,CAAC,GAAGuC,mBAAmB,EAAE1B,IAAI,CAAC;AAC/C;;AAEA;AACA,OAAO,SAAS6B,+BAA+BA,CAC7CC,EAAU,EACVnD,MAA+C,EAC/CoD,EAAU,EACV;EACA,SAAS;;EACT,IAAIpD,MAAM,CAACoB,aAAa,EAAE;IACxB,OAAO,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM;IACJK,SAAS,EAAEC,CAAC;IACZC,YAAY,EAAEN,IAAI;IAClBgC,kBAAkB,EAAEC,SAAS;IAC7BjD;EACF,CAAC,GAAGL,MAAM;EAEV,MAAMuD,eAAe,GAAIpC,IAAY,IAAK;IACxC,SAAS;;IACT,MAAMqC,SAAS,GACb,CAACrC,IAAI,GAAGiC,EAAE,GAAGA,EAAE,GAAG1B,CAAC,GAAGyB,EAAE,GAAGA,EAAE,KAAKnC,IAAI,CAACyC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGpC,IAAI,CAAC,GAAGK,CAAC,CAAC;IACjE,MAAMI,CAAC,GAAGT,IAAI,GAAG,CAAC,GAAGL,IAAI,CAACY,IAAI,CAACF,CAAC,GAAGP,IAAI,CAAC;IACxC,OACE,IAAI,IAAK,CAAC,CAAC,GAAGA,IAAI,GAAIW,CAAC,CAAC,GAAGd,IAAI,CAAC4B,GAAG,CAAEU,SAAS,GAAG,IAAI,GAAIE,SAAS,CAAC,GACnEnD,QAAQ;EAEZ,CAAC;;EAED;EACA,OAAOK,UAAU,CAAC;IAAEH,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,GAAG;IAAEG,IAAI,EAAE4C;EAAgB,CAAC,CAAC;AAChE;AAEA,OAAO,SAASG,kCAAkCA,CAChDzB,SAA+B,EAC/B0B,mBAKC,EACuC;EACxC,SAAS;;EACT,MAAM;IAAEzB;EAAQ,CAAC,GAAGD,SAAS;EAE7B,MAAM;IAAEmB,EAAE;IAAED,EAAE;IAAE7B,MAAM;IAAEsC;EAAE,CAAC,GAAGD,mBAAmB;EAEjD,MAAME,wBAAwB,GAAG7C,IAAI,CAACyC,GAAG,CAAC,CAACnC,MAAM,GAAGsC,CAAC,CAAC;EACtD,MAAME,wBAAwB,GAC5B5B,OAAO,GAAG2B,wBAAwB,IAAIV,EAAE,GAAG,CAACC,EAAE,GAAG9B,MAAM,GAAG6B,EAAE,IAAIS,CAAC,CAAC;EAEpE,MAAMG,wBAAwB,GAC5BF,wBAAwB,IACvBT,EAAE,IAAIQ,CAAC,GAAGtC,MAAM,GAAG,CAAC,CAAC,GAAGsC,CAAC,GAAGT,EAAE,GAAG7B,MAAM,GAAGA,MAAM,CAAC;EAEpD,OAAO;IACL0C,QAAQ,EAAEF,wBAAwB;IAClCG,QAAQ,EAAEF;EACZ,CAAC;AACH;AAEA,OAAO,SAASG,6BAA6BA,CAC3CjC,SAA+B,EAC/B0B,mBAOC,EACuC;EACxC,SAAS;;EACT,MAAM;IAAEzB,OAAO;IAAEnB,OAAO;IAAEkD;EAAS,CAAC,GAAGhC,SAAS;EAEhD,MAAM;IAAEZ,IAAI;IAAEuC,CAAC;IAAEtC,MAAM;IAAEC;EAAO,CAAC,GAAGoC,mBAAmB;EAEvD,MAAMP,EAAE,GAAG,CAACa,QAAQ;EACpB,MAAMd,EAAE,GAAGjB,OAAO,GAAGnB,OAAO;EAE5B,MAAMoD,IAAI,GAAGnD,IAAI,CAACoD,GAAG,CAAC7C,MAAM,GAAGqC,CAAC,CAAC;EACjC,MAAMS,IAAI,GAAGrD,IAAI,CAACsD,GAAG,CAAC/C,MAAM,GAAGqC,CAAC,CAAC;;EAEjC;EACA,MAAMW,mBAAmB,GAAGvD,IAAI,CAACyC,GAAG,CAAC,CAACpC,IAAI,GAAGC,MAAM,GAAGsC,CAAC,CAAC;EACxD,MAAMY,gBAAgB,GACpBD,mBAAmB,IAClBJ,IAAI,IAAI,CAACf,EAAE,GAAG/B,IAAI,GAAGC,MAAM,GAAG6B,EAAE,IAAI5B,MAAM,CAAC,GAAG4B,EAAE,GAAGkB,IAAI,CAAC;EAE3D,MAAMI,mBAAmB,GAAGvC,OAAO,GAAGsC,gBAAgB;EACtD;EACA,MAAME,mBAAmB,GACvBrD,IAAI,GAAGC,MAAM,GAAGkD,gBAAgB,GAChCD,mBAAmB,IAChBF,IAAI,IAAIjB,EAAE,GAAG/B,IAAI,GAAGC,MAAM,GAAG6B,EAAE,CAAC,GAAG5B,MAAM,GAAG4B,EAAE,GAAGgB,IAAI,CAAC;EAE3D,OAAO;IAAEH,QAAQ,EAAES,mBAAmB;IAAER,QAAQ,EAAES;EAAoB,CAAC;AACzE;AAEA,OAAO,SAASC,iCAAiCA,CAC/C1C,SAA+B,EAC/BjC,MAA2B,EAK3B;EACA,SAAS;;EACT,MAAM;IAAEkC,OAAO;IAAE+B,QAAQ;IAAE9B,UAAU;IAAEpB;EAAQ,CAAC,GAAGkB,SAAS;EAE5D,MAAM2C,cAAc,GAAG5E,MAAM,CAAC6E,iBAAiB,GAC1C9D,OAAO,GAAGmB,OAAO,IAAIC,UAAU,GAAGD,OAAO,IACzCnB,OAAO,GAAGmB,OAAO,IAAIC,UAAU,GAAGD,OAAQ,GAC3C,KAAK;EAET,MAAM4C,UAAU,GAAG9D,IAAI,CAACC,GAAG,CAACgD,QAAQ,CAAC,GAAGjE,MAAM,CAACqD,kBAAkB;EACjE,MAAM0B,cAAc,GAClB/D,IAAI,CAACC,GAAG,CAACiB,OAAO,GAAGnB,OAAO,CAAC,GAAGf,MAAM,CAACgF,yBAAyB;EAEhE,OAAO;IAAEJ,cAAc;IAAEE,UAAU;IAAEC;EAAe,CAAC;AACvD", "ignoreList": []}