{"version": 3, "names": ["isWorkletFunction", "ReanimatedError", "isJest", "shouldBeUseWeb", "ReanimatedModule", "makeShareableCloneOnUIRecursive", "makeShareableCloneRecursive", "IS_JEST", "SHOULD_BE_USE_WEB", "_runOnUIQueue", "setupMicrotasks", "microtasksQueue", "isExecutingMicrotasksQueue", "global", "queueMicrotask", "callback", "push", "__callMicrotasks", "index", "length", "_maybeFlushUIUpdatesQueue", "callMicrotasksOnUIThread", "callMicrotasks", "runOnUI", "worklet", "__DEV__", "_WORKLET", "args", "scheduleOnUI", "queue", "for<PERSON>ach", "executeOnUIRuntimeSync", "result", "runOnUIImmediately", "runWorkletOnJS", "runOnJS", "fun", "__remoteFunction", "scheduleOnJS", "_scheduleHostFunctionOnJS", "_scheduleRemoteFunctionOnJS", "undefined"], "sourceRoot": "../../src", "sources": ["threads.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,iBAAiB,QAAQ,kBAAe;AACjD,SAASC,eAAe,QAAQ,aAAU;AAC1C,SAASC,MAAM,EAAEC,cAAc,QAAQ,sBAAmB;AAC1D,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SACEC,+BAA+B,EAC/BC,2BAA2B,QACtB,iBAAc;AAErB,MAAMC,OAAO,GAAGL,MAAM,CAAC,CAAC;AACxB,MAAMM,iBAAiB,GAAGL,cAAc,CAAC,CAAC;;AAE1C;AACA,IAAIM,aAAsE,GAAG,EAAE;AAE/E,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,SAAS;;EAET,IAAIC,eAAkC,GAAG,EAAE;EAC3C,IAAIC,0BAA0B,GAAG,KAAK;EACtCC,MAAM,CAACC,cAAc,GAAIC,QAAoB,IAAK;IAChDJ,eAAe,CAACK,IAAI,CAACD,QAAQ,CAAC;EAChC,CAAC;EAEDF,MAAM,CAACI,gBAAgB,GAAG,MAAM;IAC9B,IAAIL,0BAA0B,EAAE;MAC9B;IACF;IACA,IAAI;MACFA,0BAA0B,GAAG,IAAI;MACjC,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,eAAe,CAACQ,MAAM,EAAED,KAAK,IAAI,CAAC,EAAE;QAC9D;QACAP,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC;MAC1B;MACAP,eAAe,GAAG,EAAE;MACpBE,MAAM,CAACO,yBAAyB,CAAC,CAAC;IACpC,CAAC,SAAS;MACRR,0BAA0B,GAAG,KAAK;IACpC;EACF,CAAC;AACH;AAEA,SAASS,wBAAwBA,CAAA,EAAG;EAClC,SAAS;;EACTR,MAAM,CAACI,gBAAgB,CAAC,CAAC;AAC3B;AAEA,OAAO,MAAMK,cAAc,GAAGd,iBAAiB,GAC3C,MAAM;EACJ;AAAA,CACD,GACDa,wBAAwB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,OAAO,SAASE,OAAOA,CACrBC,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACjB,iBAAiB,IAAIkB,QAAQ,EAAE;IAC7C,MAAM,IAAIzB,eAAe,CACvB,kJACF,CAAC;EACH;EACA,IAAIwB,OAAO,IAAI,CAACjB,iBAAiB,IAAI,CAACR,iBAAiB,CAACwB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIvB,eAAe,CAAC,2CAA2C,CAAC;EACxE;EACA,OAAO,CAAC,GAAG0B,IAAI,KAAK;IAClB,IAAIpB,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAH,gBAAgB,CAACwB,YAAY,CAC3BtB,2BAA2B,CAAC,MAAM;QAChC,SAAS;;QACTkB,OAAO,CAAC,GAAGG,IAAI,CAAC;MAClB,CAAC,CACH,CAAC;MACD;IACF;IACA,IAAIF,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACAnB,2BAA2B,CAACkB,OAAO,CAAC;MACpClB,2BAA2B,CAACqB,IAAI,CAAC;IACnC;IACAlB,aAAa,CAACO,IAAI,CAAC,CAACQ,OAAO,EAAqBG,IAAI,CAAC,CAAC;IACtD,IAAIlB,aAAa,CAACU,MAAM,KAAK,CAAC,EAAE;MAC9BL,cAAc,CAAC,MAAM;QACnB,MAAMe,KAAK,GAAGpB,aAAa;QAC3BA,aAAa,GAAG,EAAE;QAClBL,gBAAgB,CAACwB,YAAY,CAC3BtB,2BAA2B,CAAC,MAAM;UAChC,SAAS;;UACT;UACAuB,KAAK,CAACC,OAAO,CAAC,CAAC,CAACN,OAAO,EAAEG,IAAI,CAAC,KAAK;YACjCH,OAAO,CAAC,GAAGG,IAAI,CAAC;UAClB,CAAC,CAAC;UACFL,cAAc,CAAC,CAAC;QAClB,CAAC,CACH,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;;AAKA,OAAO,SAASS,sBAAsBA,CACpCP,OAA2C,EACX;EAChC,OAAO,CAAC,GAAGG,IAAI,KAAK;IAClB,OAAOvB,gBAAgB,CAAC2B,sBAAsB,CAC5CzB,2BAA2B,CAAC,MAAM;MAChC,SAAS;;MACT,MAAM0B,MAAM,GAAGR,OAAO,CAAC,GAAGG,IAAI,CAAC;MAC/B,OAAOtB,+BAA+B,CAAC2B,MAAM,CAAC;IAChD,CAAC,CACH,CAAC;EACH,CAAC;AACH;;AAEA;;AAIA;AACA,OAAO,SAASC,kBAAkBA,CAChCT,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACjB,iBAAiB,IAAIkB,QAAQ,EAAE;IAC7C,MAAM,IAAIzB,eAAe,CACvB,6JACF,CAAC;EACH;EACA,IAAIwB,OAAO,IAAI,CAACjB,iBAAiB,IAAI,CAACR,iBAAiB,CAACwB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIvB,eAAe,CACvB,sDACF,CAAC;EACH;EACA,OAAO,CAAC,GAAG0B,IAAI,KAAK;IAClBvB,gBAAgB,CAACwB,YAAY,CAC3BtB,2BAA2B,CAAC,MAAM;MAChC,SAAS;;MACTkB,OAAO,CAAC,GAAGG,IAAI,CAAC;IAClB,CAAC,CACH,CAAC;EACH,CAAC;AACH;AAcA,SAASO,cAAcA,CACrBV,OAA2C,EAC3C,GAAGG,IAAU,EACP;EACN;EACAH,OAAO,CAAC,GAAGG,IAAI,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,OAAOA,CACrBC,GAGsC,EACb;EACzB,SAAS;;EAET,IAAI5B,iBAAiB,IAAI,CAACkB,QAAQ,EAAE;IAClC;IACA,OAAO,CAAC,GAAGC,IAAI,KACbb,cAAc,CACZa,IAAI,CAACR,MAAM,GACP,MAAOiB,GAAG,CAAoC,GAAGT,IAAI,CAAC,GACrDS,GACP,CAAC;EACL;EACA,IAAIpC,iBAAiB,CAAoBoC,GAAG,CAAC,EAAE;IAC7C;IACA;;IAEA,OAAO,CAAC,GAAGT,IAAI,KACbQ,OAAO,CAACD,cAAiC,CAAC,CACxCE,GAAG,EACH,GAAGT,IACL,CAAC;EACL;EACA,IAAKS,GAAG,CAAkBC,gBAAgB,EAAE;IAC1C;IACA;IACA;IACA;IACAD,GAAG,GAAIA,GAAG,CAAkBC,gBAAgB;EAC9C;EAEA,MAAMC,YAAY,GAChB,OAAOF,GAAG,KAAK,UAAU,GACrBvB,MAAM,CAAC0B,yBAAyB,GAChC1B,MAAM,CAAC2B,2BAA2B;EAExC,OAAO,CAAC,GAAGb,IAAI,KAAK;IAClBW,YAAY,CACVF,GAAG,EAGHT,IAAI,CAACR,MAAM,GAAG,CAAC;IACX;IACCd,+BAA+B,CAACsB,IAAI,CAAC,GACtCc,SACN,CAAC;EACH,CAAC;AACH", "ignoreList": []}