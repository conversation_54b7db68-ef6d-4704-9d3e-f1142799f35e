{"version": 3, "names": ["useEvent", "useHandler", "useAnimatedScrollHandler", "handlers", "dependencies", "scrollHandlers", "onScroll", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribeForEvents", "onBeginDrag", "undefined", "push", "onEndDrag", "onMomentumBegin", "onMomentumEnd", "event", "eventName", "endsWith"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedScrollHandler.ts"], "mappings": "AAAA,YAAY;;AAOZ,SAASA,QAAQ,QAAQ,eAAY;AACrC,SAASC,UAAU,QAAQ,iBAAc;;AAmBzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA,OAAO,SAASC,wBAAwBA,CAGtCC,QAA0D,EAC1DC,YAA6B,EAC7B;EACA;EACA,MAAMC,cAAuC,GAC3C,OAAOF,QAAQ,KAAK,UAAU,GAAG;IAAEG,QAAQ,EAAEH;EAAS,CAAC,GAAGA,QAAQ;EACpE,MAAM;IAAEI,OAAO;IAAEC;EAAqB,CAAC,GAAGP,UAAU,CAGlDI,cAAc,EAA4CD,YAAY,CAAC;;EAEzE;EACA,MAAMK,kBAAkB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAIJ,cAAc,CAACK,WAAW,KAAKC,SAAS,EAAE;IAC5CF,kBAAkB,CAACG,IAAI,CAAC,mBAAmB,CAAC;EAC9C;EACA,IAAIP,cAAc,CAACQ,SAAS,KAAKF,SAAS,EAAE;IAC1CF,kBAAkB,CAACG,IAAI,CAAC,iBAAiB,CAAC;EAC5C;EACA,IAAIP,cAAc,CAACS,eAAe,KAAKH,SAAS,EAAE;IAChDF,kBAAkB,CAACG,IAAI,CAAC,uBAAuB,CAAC;EAClD;EACA,IAAIP,cAAc,CAACU,aAAa,KAAKJ,SAAS,EAAE;IAC9CF,kBAAkB,CAACG,IAAI,CAAC,qBAAqB,CAAC;EAChD;EAEA,OAAOZ,QAAQ,CACZgB,KAA4B,IAAK;IAChC,SAAS;;IACT,MAAM;MACJV,QAAQ;MACRI,WAAW;MACXG,SAAS;MACTC,eAAe;MACfC;IACF,CAAC,GAAGV,cAAc;IAClB,IAAIC,QAAQ,IAAIU,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACpDZ,QAAQ,CAACU,KAAK,EAAET,OAAO,CAAC;IAC1B,CAAC,MAAM,IAAIG,WAAW,IAAIM,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MACvER,WAAW,CAACM,KAAK,EAAET,OAAO,CAAC;IAC7B,CAAC,MAAM,IAAIM,SAAS,IAAIG,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MACnEL,SAAS,CAACG,KAAK,EAAET,OAAO,CAAC;IAC3B,CAAC,MAAM,IACLO,eAAe,IACfE,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,uBAAuB,CAAC,EACjD;MACAJ,eAAe,CAACE,KAAK,EAAET,OAAO,CAAC;IACjC,CAAC,MAAM,IACLQ,aAAa,IACbC,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAC/C;MACAH,aAAa,CAACC,KAAK,EAAET,OAAO,CAAC;IAC/B;EACF,CAAC,EACDE,kBAAkB,EAClBD;EACA;EACA;EACF,CAAC;AACH", "ignoreList": []}