{"version": 3, "names": ["isF<PERSON><PERSON>", "updateProps", "IS_FABRIC", "createViewDescriptorPaper", "screenId", "tag", "name", "createViewDescriptorFabric", "shadowNodeWrapper", "createViewDescriptor", "applyStyleForTopScreen", "screenTransitionConfig", "event", "screenDimensions", "topScreenId", "screenTransition", "topScreenStyle", "computeTopScreenStyle", "topScreenDescriptor", "value", "undefined", "applyStyleForBelowTopScreen", "belowTopScreenId", "belowTopScreenStyle", "computeBelowTopScreenStyle", "belowTopScreenDescriptor", "applyStyle"], "sourceRoot": "../../../src", "sources": ["screenTransition/styleUpdater.ts"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,QAAQ,QAAQ,uBAAoB;AAC7C,SAASC,WAAW,QAAQ,yBAAgB;AAM5C,MAAMC,SAAS,GAAGF,QAAQ,CAAC,CAAC;AAE5B,SAASG,yBAAyBA,CAACC,QAAoC,EAAE;EACvE,SAAS;;EACT,OAAO;IAAEC,GAAG,EAAED,QAAQ;IAAEE,IAAI,EAAE;EAAU,CAAC;AAC3C;AACA,SAASC,0BAA0BA,CAACH,QAAoC,EAAE;EACxE,SAAS;;EACT,OAAO;IAAEI,iBAAiB,EAAEJ;EAAS,CAAC;AACxC;AACA,MAAMK,oBAAoB,GAAGP,SAAS,GAClCK,0BAA0B,GAC1BJ,yBAAyB;AAE7B,SAASO,sBAAsBA,CAC7BC,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACT,MAAM;IAAEC,gBAAgB;IAAEC,WAAW;IAAEC;EAAiB,CAAC,GACvDJ,sBAAsB;EACxB,MAAM;IAAEK,cAAc,EAAEC;EAAsB,CAAC,GAAGF,gBAAgB;EAClE,MAAMC,cAAc,GAAGC,qBAAqB,CAACL,KAAK,EAAEC,gBAAgB,CAAC;EACrE,MAAMK,mBAAmB,GAAG;IAC1BC,KAAK,EAAE,CAACV,oBAAoB,CAACK,WAAW,CAAC;EAC3C,CAAC;EACDb,WAAW,CAACiB,mBAAmB,EAAEF,cAAc,EAAEI,SAAS,CAAC;AAC7D;AAEA,OAAO,SAASC,2BAA2BA,CACzCV,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACT,MAAM;IAAEC,gBAAgB;IAAES,gBAAgB;IAAEP;EAAiB,CAAC,GAC5DJ,sBAAsB;EACxB,MAAM;IAAEY,mBAAmB,EAAEC;EAA2B,CAAC,GAAGT,gBAAgB;EAC5E,MAAMQ,mBAAmB,GAAGC,0BAA0B,CACpDZ,KAAK,EACLC,gBACF,CAAC;EACD,MAAMY,wBAAwB,GAAG;IAC/BN,KAAK,EAAE,CAACV,oBAAoB,CAACa,gBAAgB,CAAC;EAChD,CAAC;EACDrB,WAAW,CAACwB,wBAAwB,EAAEF,mBAAmB,EAAEH,SAAS,CAAC;AACvE;AAEA,OAAO,SAASM,UAAUA,CACxBf,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACTF,sBAAsB,CAACC,sBAAsB,EAAEC,KAAK,CAAC;EACrDS,2BAA2B,CAACV,sBAAsB,EAAEC,KAAK,CAAC;AAC5D", "ignoreList": []}