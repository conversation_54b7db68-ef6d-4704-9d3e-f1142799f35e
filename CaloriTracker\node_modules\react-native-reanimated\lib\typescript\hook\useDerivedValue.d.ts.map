{"version": 3, "file": "useDerivedValue.d.ts", "sourceRoot": "", "sources": ["../../../src/hook/useDerivedValue.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,WAAW,EAAmB,MAAM,gBAAgB,CAAC;AAGnE,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAEpD,MAAM,WAAW,YAAY,CAAC,KAAK,GAAG,OAAO,CAC3C,SAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;IACjD;;;;OAIG;IACH,GAAG,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;CAChC;AAED;;;;;;;;;;;GAWG;AAEH,wBAAgB,eAAe,CAAC,KAAK,EACnC,OAAO,EAAE,MAAM,KAAK,EACpB,YAAY,CAAC,EAAE,cAAc,GAC5B,YAAY,CAAC,KAAK,CAAC,CAAC"}