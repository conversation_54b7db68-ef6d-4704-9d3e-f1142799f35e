{"version": 3, "names": ["useEffect", "startMapper", "stopMapper", "shouldBeUseWeb", "useSharedValue", "useAnimatedReaction", "prepare", "react", "dependencies", "previous", "inputs", "Object", "values", "__closure", "length", "undefined", "__workletHash", "push", "fun", "input", "value", "mapperId"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedReaction.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,QAAQ,OAAO;AAGjC,SAASC,WAAW,EAAEC,UAAU,QAAQ,YAAS;AACjD,SAASC,cAAc,QAAQ,uBAAoB;AAEnD,SAASC,cAAc,QAAQ,qBAAkB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAOA,OAAO,SAASC,mBAAmBA,CACjCC,OAA4C,EAC5CC,KAGC,EACDC,YAA6B,EAC7B;EACA,MAAMC,QAAQ,GAAGL,cAAc,CAAwB,IAAI,CAAC;EAE5D,IAAIM,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACN,OAAO,CAACO,SAAS,IAAI,CAAC,CAAC,CAAC;EAEnD,IAAIV,cAAc,CAAC,CAAC,EAAE;IACpB,IAAI,CAACO,MAAM,CAACI,MAAM,IAAIN,YAAY,EAAEM,MAAM,EAAE;MAC1C;MACAJ,MAAM,GAAGF,YAAY;IACvB;EACF;EAEA,IAAIA,YAAY,KAAKO,SAAS,EAAE;IAC9BP,YAAY,GAAG,CACb,GAAGG,MAAM,CAACC,MAAM,CAACN,OAAO,CAACO,SAAS,IAAI,CAAC,CAAC,CAAC,EACzC,GAAGF,MAAM,CAACC,MAAM,CAACL,KAAK,CAACM,SAAS,IAAI,CAAC,CAAC,CAAC,EACvCP,OAAO,CAACU,aAAa,EACrBT,KAAK,CAACS,aAAa,CACpB;EACH,CAAC,MAAM;IACLR,YAAY,CAACS,IAAI,CAACX,OAAO,CAACU,aAAa,EAAET,KAAK,CAACS,aAAa,CAAC;EAC/D;EAEAhB,SAAS,CAAC,MAAM;IACd,MAAMkB,GAAG,GAAGA,CAAA,KAAM;MAChB,SAAS;;MACT,MAAMC,KAAK,GAAGb,OAAO,CAAC,CAAC;MACvBC,KAAK,CAACY,KAAK,EAAEV,QAAQ,CAACW,KAAK,CAAC;MAC5BX,QAAQ,CAACW,KAAK,GAAGD,KAAK;IACxB,CAAC;IACD,MAAME,QAAQ,GAAGpB,WAAW,CAACiB,GAAG,EAAER,MAAM,CAAC;IACzC,OAAO,MAAM;MACXR,UAAU,CAACmB,QAAQ,CAAC;IACtB,CAAC;EACH,CAAC,EAAEb,YAAY,CAAC;AAClB", "ignoreList": []}