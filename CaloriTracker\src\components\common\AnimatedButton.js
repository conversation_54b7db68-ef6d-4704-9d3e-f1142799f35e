import React from 'react';
import { Text, StyleSheet, Pressable } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '../../theme/theme';

/**
 * Modern Animated Button Component
 */
const AnimatedButton = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  style,
  textStyle,
  colors,
  ...props
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const getButtonColors = () => {
    if (colors) return colors;
    
    switch (variant) {
      case 'primary':
        return [theme.colors.primary, theme.colors.primaryContainer];
      case 'secondary':
        return [theme.colors.secondary, theme.colors.secondaryContainer];
      case 'success':
        return [theme.colors.success, theme.colors.successContainer];
      case 'warning':
        return [theme.colors.warning, theme.colors.warningContainer];
      case 'error':
        return [theme.colors.error, theme.colors.errorContainer];
      default:
        return [theme.colors.primary, theme.colors.primaryContainer];
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: theme.spacing.sm,
          paddingHorizontal: theme.spacing.md,
          minHeight: 36,
        };
      case 'medium':
        return {
          paddingVertical: theme.spacing.md,
          paddingHorizontal: theme.spacing.lg,
          minHeight: 48,
        };
      case 'large':
        return {
          paddingVertical: theme.spacing.lg,
          paddingHorizontal: theme.spacing.xl,
          minHeight: 56,
        };
      default:
        return {
          paddingVertical: theme.spacing.md,
          paddingHorizontal: theme.spacing.lg,
          minHeight: 48,
        };
    }
  };

  const getTextStyle = () => {
    const baseStyle = {
      color: theme.colors.onPrimary,
      fontWeight: '600',
      textAlign: 'center',
    };

    switch (size) {
      case 'small':
        return { ...baseStyle, ...theme.typography.labelMedium };
      case 'medium':
        return { ...baseStyle, ...theme.typography.labelLarge };
      case 'large':
        return { ...baseStyle, ...theme.typography.titleMedium };
      default:
        return { ...baseStyle, ...theme.typography.labelLarge };
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePressIn = () => {
    if (disabled || loading) return;
    scale.value = withSpring(0.95, theme.animations.spring.snappy);
  };

  const handlePressOut = () => {
    if (disabled || loading) return;
    scale.value = withSpring(1, theme.animations.spring.bouncy);
  };

  const handlePress = () => {
    if (disabled || loading) return;
    
    // Haptic feedback simulation
    scale.value = withSpring(0.9, theme.animations.spring.snappy);
    scale.value = withSpring(1, theme.animations.spring.bouncy);
    
    if (onPress) {
      runOnJS(onPress)();
    }
  };

  const buttonStyle = {
    ...getButtonSize(),
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    ...theme.shadows.md,
  };

  return (
    <Animated.View style={[buttonStyle, animatedStyle, style, disabled && styles.disabled]}>
      <Pressable
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        style={styles.pressable}
        {...props}
      >
        <LinearGradient
          colors={getButtonColors()}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {icon && <Text style={styles.icon}>{icon}</Text>}
          <Text style={[getTextStyle(), textStyle]}>
            {loading ? 'Loading...' : title}
          </Text>
        </LinearGradient>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  pressable: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  icon: {
    fontSize: 18,
  },
  disabled: {
    opacity: 0.5,
  },
});

export default AnimatedButton;
