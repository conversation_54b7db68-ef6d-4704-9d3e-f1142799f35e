{"version": 3, "file": "useComposedEventHandler.d.ts", "sourceRoot": "", "sources": ["../../../src/hook/useComposedEventHandler.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAwB,qBAAqB,EAAE,MAAM,YAAY,CAAC;AAI9E,KAAK,wBAAwB,CAC3B,KAAK,SAAS,MAAM,EACpB,OAAO,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAC/D,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAK1C;;;;;;;;;;;;GAYG;AAEH,wBAAgB,uBAAuB,CACrC,KAAK,SAAS,MAAM,EACpB,OAAO,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAEvC,QAAQ,EAAE,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE,GACzD,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC"}