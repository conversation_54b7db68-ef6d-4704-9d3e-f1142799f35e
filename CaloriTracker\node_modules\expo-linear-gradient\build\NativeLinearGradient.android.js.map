{"version": 3, "file": "NativeLinearGradient.android.js", "sourceRoot": "", "sources": ["../src/NativeLinearGradient.android.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAC7D,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAIhD,MAAM,CAAC,OAAO,UAAU,oBAAoB,CAAC,EAC3C,MAAM,EACN,SAAS,EACT,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,MAAM,EACN,GAAG,KAAK,EACkB;IAC1B,6FAA6F;IAC7F,sEAAsE;IACtE,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IAClD,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,IAAI,CAAC,CAAC;IAEjD,2BAA2B;IAC3B,wJAAwJ;IACxJ,MAAM,oBAAoB,GAAG;QAC3B,SAAS,CAAC,mBAAmB,IAAI,YAAY;QAC7C,SAAS,CAAC,mBAAmB,IAAI,YAAY;QAC7C,SAAS,CAAC,oBAAoB,IAAI,YAAY;QAC9C,SAAS,CAAC,oBAAoB,IAAI,YAAY;QAC9C,SAAS,CAAC,uBAAuB,IAAI,YAAY;QACjD,SAAS,CAAC,uBAAuB,IAAI,YAAY;QACjD,SAAS,CAAC,sBAAsB,IAAI,YAAY;QAChD,SAAS,CAAC,sBAAsB,IAAI,YAAY;KACjD,CAAC;IAEF,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAC5B;MAAA,CAAC,wBAAwB,CACvB,KAAK,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAC/B,MAAM,CAAC,CAAC,MAAM,CAAC,CACf,UAAU,CAAC,CAAC,UAAU,CAAC,CACvB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CACnB,SAAS,CAAC,CAAC,SAAS,CAAC,CACrB,WAAW,CAAC,CAAC,oBAAoB,CAAC,CAClC,MAAM,CAAC,CAAC,MAAM,CAAC,EAEjB;MAAA,CAAC,QAAQ,CACX;IAAA,EAAE,IAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,MAAM,wBAAwB,GAAG,wBAAwB,CAAC,oBAAoB,CAAC,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport * as React from 'react';\nimport { StyleSheet, View } from 'react-native';\n\nimport { NativeLinearGradientProps } from './NativeLinearGradient.types';\n\nexport default function NativeLinearGradient({\n  colors,\n  locations,\n  startPoint,\n  endPoint,\n  children,\n  style,\n  dither,\n  ...props\n}: NativeLinearGradientProps): React.ReactElement {\n  // TODO: revisit whether we need to inherit the container's borderRadius since this issue has\n  // been resolved: https://github.com/facebook/react-native/issues/3198\n  const flatStyle = StyleSheet.flatten(style) ?? {};\n  const borderRadius = flatStyle.borderRadius ?? 0;\n\n  // This is the format from:\n  // https://developer.android.com/reference/android/graphics/Path.html#addRoundRect(android.graphics.RectF,%20float[],%20android.graphics.Path.Direction)\n  const borderRadiiPerCorner = [\n    flatStyle.borderTopLeftRadius ?? borderRadius,\n    flatStyle.borderTopLeftRadius ?? borderRadius,\n    flatStyle.borderTopRightRadius ?? borderRadius,\n    flatStyle.borderTopRightRadius ?? borderRadius,\n    flatStyle.borderBottomRightRadius ?? borderRadius,\n    flatStyle.borderBottomRightRadius ?? borderRadius,\n    flatStyle.borderBottomLeftRadius ?? borderRadius,\n    flatStyle.borderBottomLeftRadius ?? borderRadius,\n  ];\n\n  return (\n    <View {...props} style={style}>\n      <BaseNativeLinearGradient\n        style={StyleSheet.absoluteFill}\n        colors={colors}\n        startPoint={startPoint}\n        endPoint={endPoint}\n        locations={locations}\n        borderRadii={borderRadiiPerCorner}\n        dither={dither}\n      />\n      {children}\n    </View>\n  );\n}\n\nconst BaseNativeLinearGradient = requireNativeViewManager('ExpoLinearGradient');\n"]}